<template>
  <div class="tool-detail-view">
    <!-- 工具不存在 -->
    <div v-if="!tool" class="not-found text-center py-16">
      <div class="text-6xl mb-4">🔧</div>
      <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
        工具未找到
      </h2>
      <p class="text-gray-600 dark:text-gray-400 mb-6">
        您访问的工具不存在或已被移除
      </p>
      <router-link 
        to="/tools" 
        class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        ← 返回工具列表
      </router-link>
    </div>

    <!-- 工具详情 -->
    <div v-else class="tool-detail">
      <!-- 工具头部信息 -->
      <div class="tool-header bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
        <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
          <div class="flex-1">
            <!-- 面包屑导航 -->
            <nav class="breadcrumb mb-4">
              <ol class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                <li>
                  <router-link to="/" class="hover:text-blue-600 dark:hover:text-blue-400">
                    首页
                  </router-link>
                </li>
                <li class="flex items-center">
                  <svg class="w-4 h-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                  </svg>
                  <router-link 
                    :to="`/tools/${tool.category}`" 
                    class="hover:text-blue-600 dark:hover:text-blue-400"
                  >
                    {{ categoryName }}
                  </router-link>
                </li>
                <li class="flex items-center">
                  <svg class="w-4 h-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                  </svg>
                  <span class="text-gray-900 dark:text-white font-medium">{{ tool.name }}</span>
                </li>
              </ol>
            </nav>

            <!-- 工具基本信息 -->
            <div class="flex items-start gap-4">
              <div class="tool-icon text-4xl">{{ tool.icon }}</div>
              <div class="flex-1">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  {{ tool.name }}
                </h1>
                <p class="text-lg text-gray-600 dark:text-gray-300 mb-4">
                  {{ tool.description }}
                </p>
                
                <!-- 标签 -->
                <div class="flex flex-wrap gap-2 mb-4">
                  <span 
                    v-for="tag in tool.tags" 
                    :key="tag"
                    class="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full"
                  >
                    {{ tag }}
                  </span>
                  <span 
                    v-if="tool.isPopular"
                    class="px-2 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-xs rounded-full"
                  >
                    🔥 热门
                  </span>
                  <span 
                    v-if="tool.isFree"
                    class="px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs rounded-full"
                  >
                    ✨ 免费
                  </span>
                </div>

                <!-- 统计信息 -->
                <div class="flex items-center gap-6 text-sm text-gray-500 dark:text-gray-400">
                  <span class="flex items-center gap-1">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    使用 {{ tool.usageCount.toLocaleString() }} 次
                  </span>
                  <span class="flex items-center gap-1">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                    </svg>
                    更新于 {{ formatDate(tool.updatedAt) }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex flex-col gap-3 lg:flex-shrink-0">
            <button 
              @click="toggleFavorite"
              :class="[
                'flex items-center justify-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-200',
                isFavorite 
                  ? 'bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 hover:bg-red-200 dark:hover:bg-red-800' 
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              ]"
            >
              <svg class="w-4 h-4" :class="{ 'fill-current': isFavorite }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
              {{ isFavorite ? '已收藏' : '收藏' }}
            </button>
            
            <button 
              @click="shareTool"
              class="flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
              </svg>
              分享
            </button>
          </div>
        </div>
      </div>

      <!-- 工具主体内容 -->
      <div class="tool-content bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <!-- 动态组件加载 -->
        <Suspense>
          <template #default>
            <component 
              :is="toolComponent" 
              v-if="toolComponent"
              :tool="tool"
              @usage="handleToolUsage"
            />
          </template>
          <template #fallback>
            <div class="loading-state text-center py-12">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p class="text-gray-600 dark:text-gray-400">加载工具中...</p>
            </div>
          </template>
        </Suspense>
        
        <!-- 工具不可用 -->
        <div v-if="!toolComponent" class="tool-unavailable text-center py-12">
          <div class="text-4xl mb-4">🚧</div>
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            工具暂不可用
          </h3>
          <p class="text-gray-600 dark:text-gray-400">
            该工具正在开发中，敬请期待
          </p>
        </div>
      </div>

      <!-- 相关工具推荐 -->
      <div v-if="relatedTools.length > 0" class="related-tools mt-8">
        <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          相关工具推荐
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <ToolCard 
            v-for="relatedTool in relatedTools" 
            :key="relatedTool.id" 
            :tool="relatedTool" 
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, defineAsyncComponent, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useToolsStore } from '@/stores/tools'
import ToolCard from '@/components/tools/ToolCard.vue'
import type { Tool } from '@/types/tool'

const route = useRoute()
const router = useRouter()
const toolsStore = useToolsStore()

// 响应式数据
const shareTooltipVisible = ref(false)

// 计算属性
const toolId = computed(() => route.params.id as string)
const tool = computed(() => toolsStore.getToolById(toolId.value))
const isFavorite = computed(() => toolsStore.favorites.includes(toolId.value))

// 分类名称
const categoryName = computed(() => {
  if (!tool.value) return ''
  const category = toolsStore.categories.find(c => c.id === tool.value!.category)
  return category?.name || ''
})

// 动态加载工具组件
const toolComponent = computed(() => {
  if (!tool.value || tool.value.type !== 'vue') return null
  
  try {
    return defineAsyncComponent(() => 
      import(`@/components/tools/${tool.value!.component}.vue`)
    )
  } catch (error) {
    console.warn(`Tool component ${tool.value.component} not found:`, error)
    return null
  }
})

// 相关工具推荐
const relatedTools = computed(() => {
  if (!tool.value) return []
  
  return toolsStore.tools
    .filter(t => 
      t.id !== tool.value!.id && 
      (t.category === tool.value!.category || 
       t.tags.some(tag => tool.value!.tags.includes(tag)))
    )
    .slice(0, 6)
})

// 方法
function toggleFavorite() {
  if (tool.value) {
    toolsStore.toggleFavorite(tool.value.id)
  }
}

function shareTool() {
  if (!tool.value) return
  
  const url = window.location.href
  const text = `${tool.value.name} - ${tool.value.description}`
  
  if (navigator.share) {
    navigator.share({
      title: tool.value.name,
      text: text,
      url: url
    }).catch(console.error)
  } else {
    // 降级到复制链接
    navigator.clipboard.writeText(url).then(() => {
      // 可以显示一个提示
      alert('链接已复制到剪贴板')
    }).catch(() => {
      // 如果复制失败，显示链接
      prompt('复制链接:', url)
    })
  }
}

function handleToolUsage() {
  if (tool.value) {
    toolsStore.incrementUsage(tool.value.id)
  }
}

function formatDate(dateString: string) {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// 生命周期
onMounted(() => {
  // 如果工具不存在，重定向到404页面
  if (!tool.value) {
    router.replace('/404')
    return
  }
  
  // 更新页面标题
  document.title = `${tool.value.name} - ToolHub`
  
  // 初始化收藏状态
  toolsStore.initFavorites()
})
</script>

<style scoped>
.tool-detail-view {
  min-height: 60vh;
}

.not-found {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 1rem;
  border: 2px dashed #cbd5e1;
}

.dark .not-found {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  border-color: #475569;
}

.tool-unavailable {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-radius: 1rem;
  border: 2px dashed #f59e0b;
}

.dark .tool-unavailable {
  background: linear-gradient(135deg, #451a03 0%, #78350f 100%);
  border-color: #d97706;
}

.loading-state {
  background: linear-gradient(135deg, #ede9fe 0%, #ddd6fe 100%);
  border-radius: 1rem;
}

.dark .loading-state {
  background: linear-gradient(135deg, #2e1065 0%, #4c1d95 100%);
}
</style>