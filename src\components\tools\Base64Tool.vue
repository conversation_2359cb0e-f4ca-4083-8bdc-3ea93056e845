<template>
  <div class="max-w-4xl mx-auto">
    <!-- Tool Header -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
      <div class="flex items-center space-x-4 mb-4">
        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white text-xl font-bold">
          B64
        </div>
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Base64 编码/解码</h1>
          <p class="text-gray-600 dark:text-gray-400">在线 Base64 编码和解码工具</p>
        </div>
      </div>
    </div>

    <!-- Tool Content -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <!-- Mode Toggle -->
      <div class="flex items-center space-x-4 mb-6">
        <label class="text-sm font-medium text-gray-700 dark:text-gray-300">模式:</label>
        <div class="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
          <button
            @click="mode = 'encode'"
            :class="[
              'px-4 py-2 text-sm font-medium rounded-md transition-colors',
              mode === 'encode'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            ]"
          >
            编码
          </button>
          <button
            @click="mode = 'decode'"
            :class="[
              'px-4 py-2 text-sm font-medium rounded-md transition-colors',
              mode === 'decode'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            ]"
          >
            解码
          </button>
        </div>
      </div>

      <!-- Input Section -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ mode === 'encode' ? '原始文本' : 'Base64 文本' }}
          </label>
          <textarea
            v-model="inputText"
            :placeholder="mode === 'encode' ? '请输入要编码的文本...' : '请输入要解码的 Base64 文本...'"
            class="w-full h-40 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            @input="processText"
          ></textarea>
          <div class="flex items-center justify-between mt-2">
            <span class="text-xs text-gray-500 dark:text-gray-400">
              字符数: {{ inputText.length }}
            </span>
            <button
              @click="clearInput"
              class="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
            >
              清空
            </button>
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ mode === 'encode' ? 'Base64 结果' : '解码结果' }}
          </label>
          <textarea
            v-model="outputText"
            readonly
            :placeholder="mode === 'encode' ? 'Base64 编码结果将显示在这里...' : '解码结果将显示在这里...'"
            class="w-full h-40 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-600 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none resize-none"
          ></textarea>
          <div class="flex items-center justify-between mt-2">
            <span class="text-xs text-gray-500 dark:text-gray-400">
              字符数: {{ outputText.length }}
            </span>
            <button
              @click="copyResult"
              :disabled="!outputText"
              class="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 disabled:text-gray-400 disabled:cursor-not-allowed"
            >
              复制结果
            </button>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="errorMessage" class="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
        <div class="flex items-center space-x-2">
          <svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span class="text-sm text-red-700 dark:text-red-400">{{ errorMessage }}</span>
        </div>
      </div>

      <!-- Success Message -->
      <div v-if="successMessage" class="mt-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
        <div class="flex items-center space-x-2">
          <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
          <span class="text-sm text-green-700 dark:text-green-400">{{ successMessage }}</span>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex flex-wrap gap-3 mt-6">
        <button
          @click="processText"
          :disabled="!inputText"
          class="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          {{ mode === 'encode' ? '编码' : '解码' }}
        </button>
        <button
          @click="swapTexts"
          :disabled="!outputText"
          class="px-4 py-2 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:bg-gray-100 disabled:text-gray-400 dark:disabled:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-lg text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
        >
          交换输入输出
        </button>
        <button
          @click="clearAll"
          class="px-4 py-2 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
        >
          清空所有
        </button>
      </div>
    </div>

    <!-- Tool Info -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mt-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">工具说明</h3>
      <div class="prose dark:prose-invert max-w-none">
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          Base64 是一种基于 64 个可打印字符来表示二进制数据的表示方法。它常用于在处理文本数据的场合，表示、传输、存储一些二进制数据。
        </p>
        <h4 class="text-md font-medium text-gray-900 dark:text-white mb-2">使用场景：</h4>
        <ul class="list-disc list-inside text-gray-600 dark:text-gray-400 space-y-1">
          <li>电子邮件附件编码</li>
          <li>网页中嵌入图片数据</li>
          <li>API 数据传输</li>
          <li>配置文件中存储二进制数据</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const mode = ref<'encode' | 'decode'>('encode')
const inputText = ref('')
const outputText = ref('')
const errorMessage = ref('')
const successMessage = ref('')

// 监听模式变化，自动处理文本
watch(mode, () => {
  if (inputText.value) {
    processText()
  }
})

// 处理文本
const processText = () => {
  if (!inputText.value.trim()) {
    outputText.value = ''
    errorMessage.value = ''
    return
  }

  try {
    errorMessage.value = ''
    
    if (mode.value === 'encode') {
      // Base64 编码
      outputText.value = btoa(unescape(encodeURIComponent(inputText.value)))
    } else {
      // Base64 解码
      outputText.value = decodeURIComponent(escape(atob(inputText.value)))
    }
  } catch (error) {
    errorMessage.value = mode.value === 'encode' 
      ? '编码失败，请检查输入内容' 
      : '解码失败，请检查 Base64 格式是否正确'
    outputText.value = ''
  }
}

// 复制结果
const copyResult = async () => {
  if (!outputText.value) return
  
  try {
    await navigator.clipboard.writeText(outputText.value)
    successMessage.value = '复制成功！'
    setTimeout(() => {
      successMessage.value = ''
    }, 2000)
  } catch (error) {
    errorMessage.value = '复制失败，请手动复制'
    setTimeout(() => {
      errorMessage.value = ''
    }, 2000)
  }
}

// 交换输入输出
const swapTexts = () => {
  const temp = inputText.value
  inputText.value = outputText.value
  outputText.value = temp
  mode.value = mode.value === 'encode' ? 'decode' : 'encode'
  processText()
}

// 清空输入
const clearInput = () => {
  inputText.value = ''
  outputText.value = ''
  errorMessage.value = ''
  successMessage.value = ''
}

// 清空所有
const clearAll = () => {
  clearInput()
}
</script>