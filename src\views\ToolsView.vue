<template>
  <div class="tools-view">
    <!-- 页面标题和搜索 -->
    <div class="page-header mb-8">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            {{ pageTitle }}
          </h1>
          <p class="text-gray-600 dark:text-gray-300">
            {{ pageDescription }}
          </p>
        </div>
        <div class="flex-shrink-0">
          <SearchBox 
            :initial-value="searchQuery" 
            @search="handleSearch" 
            placeholder="搜索工具..."
          />
        </div>
      </div>
    </div>

    <!-- 分类筛选 -->
    <div class="category-filter mb-6">
      <div class="flex flex-wrap gap-2">
        <button
          @click="setCategory('')"
          :class="[
            'px-4 py-2 rounded-lg font-medium transition-all duration-200',
            !selectedCategory 
              ? 'bg-blue-600 text-white shadow-md' 
              : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
          ]"
        >
          全部工具
        </button>
        <button
          v-for="category in categories"
          :key="category.id"
          @click="setCategory(category.id)"
          :class="[
            'px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2',
            selectedCategory === category.id 
              ? 'bg-blue-600 text-white shadow-md' 
              : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
          ]"
        >
          <span>{{ category.icon }}</span>
          <span>{{ category.name }}</span>
          <span class="text-xs opacity-75">({{ category.toolCount }})</span>
        </button>
      </div>
    </div>

    <!-- 工具统计和排序 -->
    <div class="tools-meta flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
      <div class="text-sm text-gray-600 dark:text-gray-400">
        找到 <span class="font-semibold text-gray-900 dark:text-white">{{ filteredTools.length }}</span> 个工具
      </div>
      <div class="flex items-center gap-4">
        <label class="text-sm text-gray-600 dark:text-gray-400">排序方式:</label>
        <select 
          v-model="sortBy" 
          class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="popular">热门度</option>
          <option value="name">名称</option>
          <option value="recent">最近更新</option>
          <option value="usage">使用次数</option>
        </select>
      </div>
    </div>

    <!-- 工具网格 -->
    <div class="tools-grid">
      <div v-if="filteredTools.length === 0" class="empty-state text-center py-16">
        <div class="text-6xl mb-4">🔍</div>
        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          未找到相关工具
        </h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          尝试调整搜索关键词或选择其他分类
        </p>
        <button 
          @click="clearFilters" 
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          清除筛选
        </button>
      </div>
      
      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <ToolCard 
          v-for="tool in sortedTools" 
          :key="tool.id" 
          :tool="tool" 
          :show-category="!selectedCategory"
        />
      </div>
    </div>

    <!-- 加载更多 (如果需要分页) -->
    <div v-if="hasMore" class="load-more text-center mt-12">
      <button 
        @click="loadMore" 
        :disabled="loading"
        class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
      >
        <span v-if="loading">加载中...</span>
        <span v-else>加载更多</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useToolsStore } from '@/stores/tools'
import SearchBox from '@/components/common/SearchBox.vue'
import ToolCard from '@/components/tools/ToolCard.vue'

const route = useRoute()
const router = useRouter()
const toolsStore = useToolsStore()

// 响应式数据
const sortBy = ref<'popular' | 'name' | 'recent' | 'usage'>('popular')
const loading = ref(false)
const hasMore = ref(false) // 暂时设为false，后续可以实现分页

// 计算属性
const categories = computed(() => toolsStore.categories)
const filteredTools = computed(() => toolsStore.filteredTools)
const searchQuery = computed(() => toolsStore.searchQuery)
const selectedCategory = computed(() => toolsStore.selectedCategory)

// 页面标题和描述
const pageTitle = computed(() => {
  if (selectedCategory.value) {
    const category = categories.value.find(c => c.id === selectedCategory.value)
    return category?.name || '工具列表'
  }
  return searchQuery.value ? `搜索: ${searchQuery.value}` : '全部工具'
})

const pageDescription = computed(() => {
  if (selectedCategory.value) {
    const category = categories.value.find(c => c.id === selectedCategory.value)
    return category?.description || ''
  }
  return searchQuery.value 
    ? `为您找到 ${filteredTools.value.length} 个相关工具` 
    : '发现和使用各种实用的在线工具'
})

// 排序后的工具列表
const sortedTools = computed(() => {
  const tools = [...filteredTools.value]
  
  switch (sortBy.value) {
    case 'name':
      return tools.sort((a, b) => a.name.localeCompare(b.name))
    case 'recent':
      return tools.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
    case 'usage':
      return tools.sort((a, b) => b.usageCount - a.usageCount)
    case 'popular':
    default:
      return tools.sort((a, b) => {
        // 热门工具优先，然后按使用次数排序
        if (a.isPopular && !b.isPopular) return -1
        if (!a.isPopular && b.isPopular) return 1
        return b.usageCount - a.usageCount
      })
  }
})

// 方法
function handleSearch(query: string) {
  toolsStore.setSearchQuery(query)
  // 更新URL但不导航
  const newQuery = { ...route.query }
  if (query) {
    newQuery.q = query
  } else {
    delete newQuery.q
  }
  router.replace({ query: newQuery })
}

function setCategory(categoryId: string) {
  toolsStore.setSelectedCategory(categoryId)
  // 更新路由
  if (categoryId) {
    router.push(`/tools/${categoryId}`)
  } else {
    router.push('/tools')
  }
}

function clearFilters() {
  toolsStore.setSearchQuery('')
  toolsStore.setSelectedCategory('')
  router.push('/tools')
}

function loadMore() {
  // 实现加载更多逻辑
  loading.value = true
  // 模拟异步加载
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

// 监听路由变化
watch(
  () => route.params.category,
  (newCategory) => {
    const categoryId = Array.isArray(newCategory) ? newCategory[0] : newCategory
    toolsStore.setSelectedCategory(categoryId || '')
  },
  { immediate: true }
)

watch(
  () => route.query.q,
  (newQuery) => {
    const query = Array.isArray(newQuery) ? newQuery[0] : newQuery
    toolsStore.setSearchQuery(query || '')
  },
  { immediate: true }
)

// 生命周期
onMounted(() => {
  toolsStore.initFavorites()
})
</script>

<style scoped>
.tools-view {
  min-height: 60vh;
}

.category-filter {
  max-width: 100%;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.category-filter::-webkit-scrollbar {
  display: none;
}

.empty-state {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 1rem;
  border: 2px dashed #cbd5e1;
}

.dark .empty-state {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  border-color: #475569;
}
</style>