import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Tool, ToolCategory } from '@/types/tool'
import { mockTools, toolCategories } from '@/data/tools'

export const useToolsStore = defineStore('tools', () => {
  const tools = ref<Tool[]>(mockTools)
  const categories = ref<ToolCategory[]>(toolCategories)
  const searchQuery = ref('')
  const selectedCategory = ref<string>('')
  const favoriteTools = ref<string[]>([])

  // 计算属性
  const filteredTools = computed(() => {
    let filtered = tools.value

    // 按分类筛选
    if (selectedCategory.value) {
      filtered = filtered.filter(tool => tool.category === selectedCategory.value)
    }

    // 按搜索关键词筛选
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      filtered = filtered.filter(tool => 
        tool.name.toLowerCase().includes(query) ||
        tool.description.toLowerCase().includes(query) ||
        tool.tags.some(tag => tag.toLowerCase().includes(query))
      )
    }

    return filtered
  })

  const popularTools = computed(() => {
    return tools.value
      .filter(tool => tool.isPopular)
      .sort((a, b) => (b.usageCount || 0) - (a.usageCount || 0))
      .slice(0, 8)
  })

  const recentTools = computed(() => {
    return tools.value
      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
      .slice(0, 6)
  })

  // 方法
  function getToolById(id: string): Tool | undefined {
    return tools.value.find(tool => tool.id === id)
  }

  function getToolsByCategory(categoryId: string): Tool[] {
    return tools.value.filter(tool => tool.category === categoryId)
  }

  function toggleFavorite(toolId: string) {
    const index = favoriteTools.value.indexOf(toolId)
    if (index > -1) {
      favoriteTools.value.splice(index, 1)
    } else {
      favoriteTools.value.push(toolId)
    }
    // 保存到localStorage
    localStorage.setItem('favoriteTools', JSON.stringify(favoriteTools.value))
  }

  function isFavorite(toolId: string): boolean {
    return favoriteTools.value.includes(toolId)
  }

  function incrementUsage(toolId: string) {
    const tool = getToolById(toolId)
    if (tool) {
      tool.usageCount = (tool.usageCount || 0) + 1
    }
  }

  function setSearchQuery(query: string) {
    searchQuery.value = query
  }

  function setSelectedCategory(categoryId: string) {
    selectedCategory.value = categoryId
  }

  function clearFilters() {
    searchQuery.value = ''
    selectedCategory.value = ''
  }

  function initFavorites() {
    const saved = localStorage.getItem('favoriteTools')
    if (saved) {
      try {
        favoriteTools.value = JSON.parse(saved)
      } catch (e) {
        console.warn('Failed to parse favorite tools from localStorage')
      }
    }
  }

  return {
    tools,
    categories,
    searchQuery,
    selectedCategory,
    favoriteTools,
    filteredTools,
    popularTools,
    recentTools,
    getToolById,
    getToolsByCategory,
    toggleFavorite,
    isFavorite,
    incrementUsage,
    setSearchQuery,
    setSelectedCategory,
    clearFilters,
    initFavorites
  }
})