<template>
  <div class="home-view">
    <div class="main-content flex-1 w-full px-4 sm:px-6 lg:px-12">
      <!-- Hero Section -->
      <section class="hero-section text-center py-16 sm:py-20 mb-12">
        <div class="max-w-4xl mx-auto px-4">
          <h1 class="text-4xl font-bold mb-4">
            ToolHub
            <span class="font-light">在线工具集</span>
          </h1>
          <p class="text-lg mb-6">
            现代化的在线工具平台，提供200+实用工具，助力您的工作效率
          </p>
          
          <!-- 搜索框 -->
          <div class="max-w-xl mx-auto mb-8">
            <SearchBox @search="handleSearch" />
          </div>
          
          <!-- 快速访问按钮 -->
          <div class="flex flex-wrap justify-center gap-4">
            <button 
              v-for="category in featuredCategories" 
              :key="category.id"
              @click="navigateToCategory(category.id)"
              class="category-button"
            >
              <span class="text-xl">{{ category.icon }}</span>
              <span class="font-medium">{{ category.name }}</span>
            </button>
          </div>
        </div>
      </section>

      <!-- 热门工具 -->
      <section class="popular-tools mb-10">
        <div class="flex items-center mb-4">
          <h2 class="text-2xl font-bold text-gray-900 dark:text-white mr-4">
            🔥 热门工具
          </h2>
          <router-link 
            to="/tools" 
            class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium"
          >
            查看全部 →
          </router-link>
        </div>
        <div class="flex flex-wrap -mx-3">
          <div
            v-for="tool in popularTools"
            :key="tool.id"
            class="w-full sm:w-1/2 md:w-1/3 lg:w-1/4 xl:w-1/5 2xl:w-1/6 p-3"
          >
            <ToolCard :tool="tool" />
          </div>
        </div>
      </section>


    </div>

  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToolsStore } from '@/stores/tools'
import SearchBox from '@/components/common/SearchBox.vue'
import ToolCard from '@/components/tools/ToolCard.vue'
import CategoryCard from '@/components/tools/CategoryCard.vue'

const router = useRouter()
const toolsStore = useToolsStore()

// 计算属性
const categories = computed(() => toolsStore.categories)
const popularTools = computed(() => toolsStore.popularTools)

// 特色分类（显示在首页的快速访问按钮）
const featuredCategories = computed(() => 
  categories.value.slice(0, 6)
)

// 方法
function handleSearch(query: string) {
  toolsStore.setSearchQuery(query)
  router.push('/tools')
}

function navigateToCategory(categoryId: string) {
  toolsStore.setSelectedCategory(categoryId)
  router.push(`/tools/${categoryId}`)
}

// 生命周期
onMounted(() => {
  toolsStore.initFavorites()
})
</script>

<style scoped>
.hero-section {
  /* Ghibli-inspired pastoral gradient */
  background: linear-gradient(135deg, #a8d8ea 0%, #d5e8d4 60%, #fcdfa6 100%);
  border-radius: 24px; /* Organic, rounded shape */
  border: 1px solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 8px 32px rgba(149, 157, 165, 0.2);
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.hero-section:hover {
  transform: translateY(-5px);
}

.dark .hero-section {
  /* A slightly moodier, night-sky version */
  background: linear-gradient(135deg, #2c3e50 0%, #4a5568 100%);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.hero-section h1 {
  color: #2c5282; /* Deep, earthy blue */
  text-shadow: 1px 1px 3px rgba(255, 255, 255, 0.5);
}
.dark .hero-section h1 {
  color: #e2e8f0;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

.hero-section p {
  color: #4a5568; /* Softer, darker gray */
}
.dark .hero-section p {
  color: #cbd5e1; /* Lighter slate for dark mode */
}

.category-button {
  padding: 0.6rem 1.2rem;
  background-color: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(5px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.6);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  color: #334155;
}

.category-button:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  background-color: rgba(255, 255, 255, 0.6);
}

.dark .category-button {
  background-color: rgba(45, 55, 72, 0.4);
  border-color: rgba(255, 255, 255, 0.2);
  color: #e2e8f0;
}

.dark .category-button:hover {
  background-color: rgba(45, 55, 72, 0.7);
}
</style>