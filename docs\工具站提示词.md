**目标**：构建一个基于Vue 3技术栈的现代化、组件化、响应式的AI工具集网站（ToolHub），专注于提供卓越的用户体验和清晰的技术架构。

## 📋 核心功能要求
1.  **组件化设计**：整个应用由独立、可复用的Vue组件构成。
2.  **动态路由**：通过URL驱动内容展示，支持按分类浏览和直达工具详情页。
3.  **响应式交互**：界面元素（如搜索）能实时响应用户输入，无需刷新页面。
4.  **可定制主题**：支持多主题切换（如明/暗模式），并记忆用户选择。
5.  **全平台适配**：确保在桌面、平板和移动设备上都有一致且良好的浏览体验。

## 🎨 视觉设计规范

### 配色与主题
- **色彩系统**：定义一组核心的CSS变量（CSS Custom Properties）来管理主色、辅助色、背景色和文本色。
- **主题实现**：通过切换根元素上的特定class（如 `.dark`），应用不同的颜色变量，从而实现主题切换。

### 设计元素
- **字体**: 优先使用操作系统默认的UI字体，保证跨平台的一致性和性能。
- **圆角**: 统一使用大圆角（如 8px 或 12px）来营造现代感。
- **阴影**: 使用柔和的阴影效果增加元素的层次感，并在交互时（如悬停）加深阴影。
- **间距**: 遵循统一的间距规范（如8px网格系统），确保布局的协调性。
- **动画**: 交互动画应保持简洁、快速（约0.3秒），使用平滑的缓动函数。

## 🏗️ 布局与原型

### 桌面端原型 (Desktop Prototype)
```
+----------------------+--------------------------------------------------+
|                      | [Logo] ToolHub       [<   Search...   >]  [☀️/🌙]    |
|       Sidebar        +--------------------------------------------------+
| (分类导航)           |                                                  |
|                      |                                                  |
| - 开发工具 [25]      |        主内容区 (Main Content / <router-view>)     |
| - 编码加密 [20]      |                                                  |
| -*转换工具 [12]*     |   +-----------+  +-----------+  +-----------+     |
| - 文本工具 [25]      |   | Tool Card |  | Tool Card |  | Tool Card |     |
| - ...                |   +-----------+  +-----------+  +-----------+     |
|                      |                                                  |
|                      |   +-----------+  +-----------+  +-----------+     |
|                      |   | Tool Card |  | Tool Card |  | Tool Card |     |
|                      |   +-----------+  +-----------+  +-----------+     |
|                      |                                                  |
+----------------------+--------------------------------------------------+
```

### 移动端原型 (Mobile Prototype)
```
+--------------------------------+
| [☰] [Logo] ToolHub   [🔍] [☀️] |
+--------------------------------+
|                                |
|      主内容区 (Main Content)   |
|                                |
|      +-------------------+     |
|      |    Tool Card      |     |
|      +-------------------+     |
|                                |
|      +-------------------+     |
|      |    Tool Card      |     |
|      +-------------------+     |
|              ...               |
+--------------------------------+
```
*说明：移动端侧边栏（`[☰]`）默认隐藏，点击后从左侧滑出。*

## 🃏 工具卡片设计 (Tool Card)

### 卡片结构原型
```
+--------------------------+
|                          |
|          [ICON]          |
|      (工具的SVG图标)     |
|                          |
|      工具卡片标题        |
|   这是一个关于此工具的   |
|   非常简短的描述...      |
|                          |
|  [标签一] [标签二]       |
|                          |
|   +------------------+   |
|   |    使用此工具    |   |
|   +------------------+   |
|                          |
+--------------------------+
```

### 交互效果
- **悬停**: 卡片轻微上浮，阴影加深，给用户提供明确的视觉反馈。
- **加载**: 在数据请求期间，使用骨架屏（Skeleton Screen）作为占位符，提升加载体验。

## ⚡ 技术架构核心

### 1. 核心框架 (Core Framework)
- **视图层**: **Vue 3**，采用组合式API (`<script setup>`) 进行组件开发。
- **构建工具**: **Vite**，提供极速的开发服务器和优化的构建输出。
- **样式方案**: **Tailwind CSS**，用于快速构建一致、可定制的界面。

### 2. 状态管理 (State Management)
- **核心原则**: 分离组件状态和全局应用状态。
- **实现方式**:
    - **组件内部状态**: 使用 Vue 的响应式系统 (`ref`, `reactive`)。
    - **全局应用状态**: 引入 **Pinia** 进行集中管理（如用户信息、主题、收藏列表），它提供了类型安全、模块化的状态存储。

### 3. 逻辑复用 (Logic Reusability)
- **核心原则**: 将可复用的业务逻辑从组件中抽离。
- **实现方式**:
    - **组合式函数 (Composables)**: 自定义组合式函数来封装业务逻辑。
    - **工具集**: 引入 **VueUse**，它提供了大量即用型的组合式函数（如本地存储、剪贴板、事件监听等），极大提升开发效率。

### 4. 路由系统 (Routing)
- **核心原则**: 构建清晰、可预测的路由结构。
- **实现方式**: 使用 **Vue Router** 定义动态路由，支持按分类浏览和工具详情页的直接访问。

### 5. 静态类型 (Static Typing)
- **核心原则**: 在开发阶段引入类型检查，提升代码健壮性和可维护性。
- **实现方式**: 全面采用 **TypeScript**，利用其强大的类型推断和接口定义能力，确保数据流的正确性。

### 6. 测试 (Testing)
- **核心原则**: 保证代码质量和应用稳定性。
- **实现方式**:
    - **单元测试**: 使用 **Vitest** 配合 **Vue Test Utils** 对核心工具函数和独立组件进行测试。
    - **端到端测试**: 使用 **Playwright** 模拟真实用户操作，测试完整的业务流程。

### 7. 性能优化 (Performance)
- **核心原则**: 确保应用快速加载和流畅运行。
- **实现方式**: 对路由和组件进行懒加载（Code Splitting）。对长列表数据采用虚拟滚动技术。利用Vite的构建优化能力。

### 8. 无障碍访问 (Accessibility - A11y)
- **核心原则**: 保证应用对所有用户都是可用的。
- **实现方式**: 遵循WAI-ARIA标准，为所有可交互元素提供正确的语义和键盘导航支持。
