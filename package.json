{"name": "toolhub", "version": "0.0.0", "private": true, "type": "module", "scripts": {"build": "run-p type-check \"build-only {@}\" --", "build-only": "vite build", "dev": "vite", "preview": "vite preview", "test:e2e": "playwright test", "test:e2e:dev": "playwright test --ui", "test:unit": "vitest", "type-check": "vue-tsc --build --force"}, "dependencies": {"@heroicons/vue": "^2.2.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "pinia": "^2.1.7", "vue": "^3.4.29", "vue-json-pretty": "^2.5.0", "vue-router": "^4.3.3"}, "devDependencies": {"@playwright/test": "^1.44.1", "@tsconfig/node20": "^20.1.4", "@types/jsdom": "^21.1.7", "@types/node": "^20.14.5", "@vitejs/plugin-vue": "^5.0.5", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "jsdom": "^24.1.0", "npm-run-all2": "^6.2.0", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "typescript": "~5.4.0", "vite": "^5.3.1", "vitest": "^1.6.0", "vue-tsc": "^2.0.21"}}