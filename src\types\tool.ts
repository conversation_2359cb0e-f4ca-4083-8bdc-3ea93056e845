export interface Tool {
  id: string
  name: string
  description: string
  category: string
  tags: string[]
  icon: string
  isPopular?: boolean
  isFree?: boolean
  usageCount?: number
  createdAt: string
  updatedAt: string
  // 工具类型：vue组件 或 HTML页面
  type: 'vue' | 'html'
  // Vue组件路径（type为vue时使用）
  component?: string
  // HTML页面路径（type为html时使用）
  htmlPath?: string
  // 工具配置
  config?: ToolConfig
}

export interface ToolConfig {
  // 是否需要全屏显示
  fullscreen?: boolean
  // 是否支持文件上传
  fileUpload?: boolean
  // 支持的文件类型
  acceptedFileTypes?: string[]
  // 最大文件大小（MB）
  maxFileSize?: number
  // 其他配置
  [key: string]: any
}

export interface ToolCategory {
  id: string
  name: string
  description: string
  icon: string
  color: string
  toolCount: number
}

export interface ToolSearchFilters {
  query: string
  category: string
  tags: string[]
  isPopular?: boolean
  isFree?: boolean
}