import { defineStore } from 'pinia'
import type { Tool } from '@/types'

export interface FavoritesState {
  favorites: Tool[]
  lastUpdated: string | null
}

export const useFavoritesStore = defineStore('favorites', {
  state: (): FavoritesState => ({
    favorites: [],
    lastUpdated: null
  }),

  getters: {
    favoriteIds: (state) => state.favorites.map(tool => tool.id),
    
    favoriteCount: (state) => state.favorites.length,
    
    favoritesByCategory: (state) => {
      const grouped: Record<string, Tool[]> = {}
      state.favorites.forEach(tool => {
        if (!grouped[tool.categoryId]) {
          grouped[tool.categoryId] = []
        }
        grouped[tool.categoryId].push(tool)
      })
      return grouped
    },
    
    recentFavorites: (state) => {
      return [...state.favorites]
        .sort((a, b) => {
          const aTime = a.favoriteAddedAt ? new Date(a.favoriteAddedAt).getTime() : 0
          const bTime = b.favoriteAddedAt ? new Date(b.favoriteAddedAt).getTime() : 0
          return bTime - aTime
        })
        .slice(0, 10)
    },
    
    topRatedFavorites: (state) => {
      return [...state.favorites]
        .filter(tool => tool.rating && tool.rating > 0)
        .sort((a, b) => (b.rating || 0) - (a.rating || 0))
        .slice(0, 10)
    }
  },

  actions: {
    // 初始化收藏夹（从本地存储加载）
    initFavorites() {
      try {
        const stored = localStorage.getItem('toolhub-favorites')
        if (stored) {
          const data = JSON.parse(stored)
          this.favorites = data.favorites || []
          this.lastUpdated = data.lastUpdated || null
        }
      } catch (error) {
        console.error('Failed to load favorites from localStorage:', error)
        this.favorites = []
        this.lastUpdated = null
      }
    },

    // 保存到本地存储
    saveFavorites() {
      try {
        const data = {
          favorites: this.favorites,
          lastUpdated: new Date().toISOString()
        }
        localStorage.setItem('toolhub-favorites', JSON.stringify(data))
        this.lastUpdated = data.lastUpdated
      } catch (error) {
        console.error('Failed to save favorites to localStorage:', error)
      }
    },

    // 添加收藏
    addFavorite(tool: Tool) {
      // 检查是否已经收藏
      if (this.isFavorite(tool.id)) {
        return false
      }

      // 添加收藏时间戳
      const favoriteTime = new Date().toISOString()
      const toolWithFavoriteTime = {
        ...tool,
        favoriteAddedAt: favoriteTime
      }

      this.favorites.push(toolWithFavoriteTime)
      this.saveFavorites()
      return true
    },

    // 移除收藏
    removeFavorite(toolId: string) {
      const index = this.favorites.findIndex(tool => tool.id === toolId)
      if (index > -1) {
        this.favorites.splice(index, 1)
        this.saveFavorites()
        return true
      }
      return false
    },

    // 检查是否已收藏
    isFavorite(toolId: string): boolean {
      return this.favorites.some(tool => tool.id === toolId)
    },

    // 切换收藏状态
    toggleFavorite(tool: Tool): boolean {
      if (this.isFavorite(tool.id)) {
        return !this.removeFavorite(tool.id)
      } else {
        return this.addFavorite(tool)
      }
    },

    // 获取收藏的工具
    getFavorite(toolId: string): Tool | undefined {
      return this.favorites.find(tool => tool.id === toolId)
    },

    // 批量添加收藏
    addFavorites(tools: Tool[]) {
      let addedCount = 0
      tools.forEach(tool => {
        if (this.addFavorite(tool)) {
          addedCount++
        }
      })
      return addedCount
    },

    // 批量移除收藏
    removeFavorites(toolIds: string[]) {
      let removedCount = 0
      toolIds.forEach(toolId => {
        if (this.removeFavorite(toolId)) {
          removedCount++
        }
      })
      return removedCount
    },

    // 清空收藏夹
    clearFavorites() {
      this.favorites = []
      this.saveFavorites()
    },

    // 搜索收藏的工具
    searchFavorites(query: string): Tool[] {
      if (!query.trim()) {
        return this.favorites
      }

      const searchTerm = query.toLowerCase().trim()
      return this.favorites.filter(tool => {
        return (
          tool.name.toLowerCase().includes(searchTerm) ||
          tool.description.toLowerCase().includes(searchTerm) ||
          tool.tags?.some(tag => tag.toLowerCase().includes(searchTerm)) ||
          tool.keywords?.some(keyword => keyword.toLowerCase().includes(searchTerm))
        )
      })
    },

    // 按分类过滤收藏
    getFavoritesByCategory(categoryId: string): Tool[] {
      return this.favorites.filter(tool => tool.categoryId === categoryId)
    },

    // 获取收藏统计信息
    getFavoritesStats() {
      const stats = {
        total: this.favoriteCount,
        byCategory: {} as Record<string, number>,
        averageRating: 0,
        totalUsage: 0,
        recentlyAdded: 0 // 最近7天添加的收藏数量
      }

      // 按分类统计
      this.favorites.forEach(tool => {
        stats.byCategory[tool.categoryId] = (stats.byCategory[tool.categoryId] || 0) + 1
      })

      // 平均评分
      const ratedTools = this.favorites.filter(tool => tool.rating && tool.rating > 0)
      if (ratedTools.length > 0) {
        stats.averageRating = ratedTools.reduce((sum, tool) => sum + (tool.rating || 0), 0) / ratedTools.length
      }

      // 总使用次数
      stats.totalUsage = this.favorites.reduce((sum, tool) => sum + (tool.usageCount || 0), 0)

      // 最近7天添加的收藏
      const sevenDaysAgo = new Date()
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
      stats.recentlyAdded = this.favorites.filter(tool => {
        return tool.favoriteAddedAt && new Date(tool.favoriteAddedAt) > sevenDaysAgo
      }).length

      return stats
    },

    // 导出收藏夹
    exportFavorites(): string {
      const exportData = {
        version: '1.0',
        exportedAt: new Date().toISOString(),
        favorites: this.favorites.map(tool => ({
          id: tool.id,
          name: tool.name,
          description: tool.description,
          categoryId: tool.categoryId,
          url: tool.url,
          tags: tool.tags,
          favoriteAddedAt: tool.favoriteAddedAt
        }))
      }
      return JSON.stringify(exportData, null, 2)
    },

    // 导入收藏夹
    importFavorites(jsonData: string): { success: boolean; imported: number; errors: string[] } {
      const result = {
        success: false,
        imported: 0,
        errors: [] as string[]
      }

      try {
        const data = JSON.parse(jsonData)
        
        if (!data.favorites || !Array.isArray(data.favorites)) {
          result.errors.push('Invalid data format: favorites array not found')
          return result
        }

        data.favorites.forEach((tool: any, index: number) => {
          try {
            if (!tool.id || !tool.name) {
              result.errors.push(`Tool at index ${index}: missing required fields (id, name)`)
              return
            }

            // 检查是否已存在
            if (!this.isFavorite(tool.id)) {
              this.addFavorite(tool as Tool)
              result.imported++
            }
          } catch (error) {
            result.errors.push(`Tool at index ${index}: ${error}`)
          }
        })

        result.success = result.imported > 0
      } catch (error) {
        result.errors.push(`JSON parsing error: ${error}`)
      }

      return result
    },

    // 更新收藏工具的信息（当工具信息发生变化时）
    updateFavoriteTool(updatedTool: Tool) {
      const index = this.favorites.findIndex(tool => tool.id === updatedTool.id)
      if (index > -1) {
        // 保留收藏时间戳
        const favoriteAddedAt = this.favorites[index].favoriteAddedAt
        this.favorites[index] = {
          ...updatedTool,
          favoriteAddedAt
        }
        this.saveFavorites()
        return true
      }
      return false
    }
  }
})

// 收藏夹相关的工具函数
export const favoritesUtils = {
  // 检查工具是否被收藏
  isFavorite: (toolId: string): boolean => {
    const favorites = useFavoritesStore()
    return favorites.isFavorite(toolId)
  },

  // 切换收藏状态
  toggleFavorite: (tool: Tool): boolean => {
    const favorites = useFavoritesStore()
    return favorites.toggleFavorite(tool)
  },

  // 获取收藏数量
  getFavoriteCount: (): number => {
    const favorites = useFavoritesStore()
    return favorites.favoriteCount
  },

  // 获取收藏列表
  getFavorites: (): Tool[] => {
    const favorites = useFavoritesStore()
    return favorites.favorites
  }
}

// 类型导出
export type { Tool }