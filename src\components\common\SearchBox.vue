<template>
  <div class="relative">
    <div class="relative">
      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </div>
      <input
        v-model="searchQuery"
        type="text"
        :placeholder="placeholder"
        class="block w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
        @keyup.enter="handleSearch"
        @input="handleInput"
      />
      <div class="absolute inset-y-0 right-0 flex items-center">
        <button
          v-if="searchQuery"
          @click="clearSearch"
          class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          title="清除搜索"
        >
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        <button
          @click="handleSearch"
          class="p-2 mr-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
          title="搜索"
        >
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Search Suggestions -->
    <div
      v-if="showSuggestions && suggestions.length > 0"
      class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-y-auto"
    >
      <ul class="py-1">
        <li
          v-for="(suggestion, index) in suggestions"
          :key="index"
          @click="selectSuggestion(suggestion)"
          class="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer flex items-center space-x-2"
        >
          <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <span>{{ suggestion }}</span>
        </li>
      </ul>
    </div>

    <!-- Recent Searches -->
    <div
      v-if="showRecentSearches && recentSearches.length > 0"
      class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg"
    >
      <div class="p-3 border-b border-gray-200 dark:border-gray-600">
        <div class="flex items-center justify-between">
          <h4 class="text-sm font-medium text-gray-900 dark:text-white">最近搜索</h4>
          <button
            @click="clearRecentSearches"
            class="text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
          >
            清除
          </button>
        </div>
      </div>
      <ul class="py-1">
        <li
          v-for="(search, index) in recentSearches"
          :key="index"
          @click="selectSuggestion(search)"
          class="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer flex items-center space-x-2"
        >
          <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{{ search }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToolsStore } from '@/stores/tools'

interface Props {
  placeholder?: string
  modelValue?: string
  size?: 'sm' | 'md' | 'lg'
  showSuggestions?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '搜索工具...',
  modelValue: '',
  size: 'md',
  showSuggestions: true
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  search: [query: string]
}>()

const router = useRouter()
const toolsStore = useToolsStore()

const searchQuery = ref(props.modelValue)
const showSuggestions = ref(false)
const showRecentSearches = ref(false)
const recentSearches = ref<string[]>([])

// 监听 modelValue 变化
watch(() => props.modelValue, (newValue) => {
  searchQuery.value = newValue
})

// 监听搜索输入变化
watch(searchQuery, (newValue) => {
  emit('update:modelValue', newValue)
})

// 搜索建议
const suggestions = computed(() => {
  if (!searchQuery.value || searchQuery.value.length < 2) {
    return []
  }

  const query = searchQuery.value.toLowerCase()
  const toolNames = toolsStore.tools.map(tool => tool.name)
  const toolTags = toolsStore.tools.flatMap(tool => tool.tags)
  const allSuggestions = [...new Set([...toolNames, ...toolTags])]

  return allSuggestions
    .filter(item => item.toLowerCase().includes(query))
    .slice(0, 5)
})

// 处理输入
const handleInput = () => {
  showRecentSearches.value = false
  showSuggestions.value = searchQuery.value.length >= 2
}

// 处理搜索
const handleSearch = () => {
  if (!searchQuery.value.trim()) return

  const query = searchQuery.value.trim()
  
  // 添加到最近搜索
  addToRecentSearches(query)
  
  // 隐藏建议
  showSuggestions.value = false
  showRecentSearches.value = false
  
  // 触发搜索事件
  emit('search', query)
  
  // 导航到工具页面
  router.push({ path: '/tools', query: { search: query } })
}

// 选择建议
const selectSuggestion = (suggestion: string) => {
  searchQuery.value = suggestion
  handleSearch()
}

// 清除搜索
const clearSearch = () => {
  searchQuery.value = ''
  showSuggestions.value = false
  showRecentSearches.value = false
  emit('update:modelValue', '')
}

// 添加到最近搜索
const addToRecentSearches = (query: string) => {
  const searches = recentSearches.value.filter(item => item !== query)
  searches.unshift(query)
  recentSearches.value = searches.slice(0, 5)
  localStorage.setItem('recent-searches', JSON.stringify(recentSearches.value))
}

// 清除最近搜索
const clearRecentSearches = () => {
  recentSearches.value = []
  localStorage.removeItem('recent-searches')
  showRecentSearches.value = false
}

// 处理点击外部
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    showSuggestions.value = false
    showRecentSearches.value = false
  }
}

// 处理焦点
const handleFocus = () => {
  if (!searchQuery.value && recentSearches.value.length > 0) {
    showRecentSearches.value = true
  } else if (searchQuery.value.length >= 2) {
    showSuggestions.value = true
  }
}

// 生命周期
onMounted(() => {
  // 加载最近搜索
  const saved = localStorage.getItem('recent-searches')
  if (saved) {
    try {
      recentSearches.value = JSON.parse(saved)
    } catch (e) {
      console.warn('Failed to parse recent searches:', e)
    }
  }

  // 添加全局点击监听
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>