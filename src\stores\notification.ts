import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number // 持续时间，0 表示不自动关闭
  createdAt: number
  actions?: NotificationAction[]
}

export interface NotificationAction {
  label: string
  action: () => void
  style?: 'primary' | 'secondary' | 'danger'
}

export const useNotificationStore = defineStore('notification', () => {
  const notifications = ref<Notification[]>([])
  const maxNotifications = ref(5)

  // 添加通知
  function addNotification(notification: Omit<Notification, 'id' | 'createdAt'>) {
    const id = generateId()
    const createdAt = Date.now()
    const duration = notification.duration ?? getDefaultDuration(notification.type)
    
    const newNotification: Notification = {
      ...notification,
      id,
      createdAt,
      duration,
    }

    notifications.value.unshift(newNotification)

    // 限制通知数量
    if (notifications.value.length > maxNotifications.value) {
      notifications.value = notifications.value.slice(0, maxNotifications.value)
    }

    // 自动移除
    if (duration > 0) {
      setTimeout(() => {
        removeNotification(id)
      }, duration)
    }

    return id
  }

  // 移除通知
  function removeNotification(id: string) {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  // 清空所有通知
  function clearNotifications() {
    notifications.value = []
  }

  // 更新进度（用于进度条动画）
  function updateProgress() {
    const now = Date.now()
    notifications.value.forEach(notification => {
      if (notification.duration && notification.duration > 0) {
        const elapsed = now - notification.createdAt
        if (elapsed >= notification.duration) {
          removeNotification(notification.id)
        }
      }
    })
  }

  // 便捷方法
  function success(title: string, message?: string, duration?: number) {
    return addNotification({ type: 'success', title, message, duration })
  }

  function error(title: string, message?: string, duration?: number) {
    return addNotification({ type: 'error', title, message, duration })
  }

  function warning(title: string, message?: string, duration?: number) {
    return addNotification({ type: 'warning', title, message, duration })
  }

  function info(title: string, message?: string, duration?: number) {
    return addNotification({ type: 'info', title, message, duration })
  }

  // 带操作按钮的通知
  function confirm(
    title: string,
    message: string,
    onConfirm: () => void,
    onCancel?: () => void
  ) {
    const actions: NotificationAction[] = [
      {
        label: '确认',
        action: () => {
          onConfirm()
        },
        style: 'primary',
      },
    ]

    if (onCancel) {
      actions.push({
        label: '取消',
        action: onCancel,
        style: 'secondary',
      })
    }

    return addNotification({
      type: 'warning',
      title,
      message,
      duration: 0, // 不自动关闭
      actions,
    })
  }

  // 获取默认持续时间
  function getDefaultDuration(type: Notification['type']): number {
    const durations = {
      success: 3000,
      info: 4000,
      warning: 5000,
      error: 6000,
    }
    return durations[type]
  }

  // 生成唯一ID
  function generateId(): string {
    return `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  // 设置最大通知数量
  function setMaxNotifications(max: number) {
    maxNotifications.value = max
    if (notifications.value.length > max) {
      notifications.value = notifications.value.slice(0, max)
    }
  }

  // 获取通知统计
  function getStats() {
    const stats = {
      total: notifications.value.length,
      success: 0,
      error: 0,
      warning: 0,
      info: 0,
    }

    notifications.value.forEach(notification => {
      stats[notification.type]++
    })

    return stats
  }

  return {
    // 状态
    notifications,
    maxNotifications,

    // 动作
    addNotification,
    removeNotification,
    clearNotifications,
    updateProgress,
    setMaxNotifications,

    // 便捷方法
    success,
    error,
    warning,
    info,
    confirm,

    // 工具方法
    getStats,
  }
})

// 全局通知实例
let globalNotificationStore: ReturnType<typeof useNotificationStore> | null = null

// 获取全局通知实例
export function getGlobalNotification() {
  if (!globalNotificationStore) {
    globalNotificationStore = useNotificationStore()
  }
  return globalNotificationStore
}

// 全局通知方法
export const notification = {
  success: (title: string, message?: string, duration?: number) => {
    return getGlobalNotification().success(title, message, duration)
  },
  error: (title: string, message?: string, duration?: number) => {
    return getGlobalNotification().error(title, message, duration)
  },
  warning: (title: string, message?: string, duration?: number) => {
    return getGlobalNotification().warning(title, message, duration)
  },
  info: (title: string, message?: string, duration?: number) => {
    return getGlobalNotification().info(title, message, duration)
  },
  confirm: (
    title: string,
    message: string,
    onConfirm: () => void,
    onCancel?: () => void
  ) => {
    return getGlobalNotification().confirm(title, message, onConfirm, onCancel)
  },
}

// 通知类型
export type NotificationType = Notification['type']

// 通知配置
export interface NotificationConfig {
  maxNotifications?: number
  defaultDuration?: {
    success?: number
    error?: number
    warning?: number
    info?: number
  }
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center'
  showProgress?: boolean
  pauseOnHover?: boolean
}

// 默认配置
export const defaultNotificationConfig: Required<NotificationConfig> = {
  maxNotifications: 5,
  defaultDuration: {
    success: 3000,
    error: 6000,
    warning: 5000,
    info: 4000,
  },
  position: 'top-right',
  showProgress: true,
  pauseOnHover: true,
}