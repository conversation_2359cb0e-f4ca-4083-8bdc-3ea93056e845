<template>
  <div class="about-view">
    <!-- Hero Section -->
    <section class="hero-section text-center py-16 mb-12">
      <div class="max-w-4xl mx-auto">
        <h1 class="text-5xl font-bold text-white mb-6">
          关于 ToolHub
        </h1>
        <p class="text-xl text-white/90 mb-8">
          致力于打造最好用的在线工具平台
        </p>
      </div>
    </section>

    <!-- 项目介绍 -->
    <section class="project-intro mb-16">
      <div class="max-w-4xl mx-auto">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-6">
              🚀 我们的使命
            </h2>
            <p class="text-lg text-gray-600 dark:text-gray-300 mb-6">
              ToolHub 是一个现代化的在线工具平台，旨在为开发者、设计师和普通用户提供高效、易用的在线工具集合。我们相信好的工具能够显著提升工作效率，让复杂的任务变得简单。
            </p>
            <p class="text-lg text-gray-600 dark:text-gray-300">
              无论您是需要格式化代码、转换数据格式、生成二维码，还是进行各种计算和验证，ToolHub 都能为您提供专业、可靠的解决方案。
            </p>
          </div>
          <div class="text-center">
            <div class="inline-block p-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-2xl">
              <div class="text-6xl mb-4">🛠️</div>
              <div class="text-white font-bold text-2xl">200+</div>
              <div class="text-white/80">实用工具</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 特色功能 -->
    <section class="features mb-16">
      <div class="max-w-6xl mx-auto">
        <h2 class="text-3xl font-bold text-gray-900 dark:text-white text-center mb-12">
          ✨ 平台特色
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div class="feature-card bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
            <div class="text-4xl mb-4">⚡</div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">
              极速体验
            </h3>
            <p class="text-gray-600 dark:text-gray-300">
              基于现代前端技术栈构建，提供流畅的用户体验和快速的响应速度。
            </p>
          </div>
          
          <div class="feature-card bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
            <div class="text-4xl mb-4">🔒</div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">
              隐私安全
            </h3>
            <p class="text-gray-600 dark:text-gray-300">
              所有数据处理都在本地进行，不会上传到服务器，确保您的数据安全和隐私。
            </p>
          </div>
          
          <div class="feature-card bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
            <div class="text-4xl mb-4">📱</div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">
              响应式设计
            </h3>
            <p class="text-gray-600 dark:text-gray-300">
              完美适配各种设备和屏幕尺寸，无论是桌面端还是移动端都能获得最佳体验。
            </p>
          </div>
          
          <div class="feature-card bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
            <div class="text-4xl mb-4">🎨</div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">
              现代界面
            </h3>
            <p class="text-gray-600 dark:text-gray-300">
              采用现代化的设计语言，支持深色模式，为您提供舒适的视觉体验。
            </p>
          </div>
          
          <div class="feature-card bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
            <div class="text-4xl mb-4">🔍</div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">
              智能搜索
            </h3>
            <p class="text-gray-600 dark:text-gray-300">
              强大的搜索功能和分类筛选，帮助您快速找到需要的工具。
            </p>
          </div>
          
          <div class="feature-card bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
            <div class="text-4xl mb-4">💝</div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">
              完全免费
            </h3>
            <p class="text-gray-600 dark:text-gray-300">
              所有工具完全免费使用，无需注册，无广告干扰，专注于提供纯净的工具体验。
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- 技术栈 -->
    <section class="tech-stack mb-16">
      <div class="max-w-4xl mx-auto">
        <h2 class="text-3xl font-bold text-gray-900 dark:text-white text-center mb-12">
          🛠️ 技术栈
        </h2>
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-8">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div class="tech-item text-center">
              <div class="text-3xl mb-2">⚡</div>
              <div class="font-semibold text-gray-900 dark:text-white">Vue 3</div>
              <div class="text-sm text-gray-600 dark:text-gray-400">前端框架</div>
            </div>
            <div class="tech-item text-center">
              <div class="text-3xl mb-2">📘</div>
              <div class="font-semibold text-gray-900 dark:text-white">TypeScript</div>
              <div class="text-sm text-gray-600 dark:text-gray-400">类型安全</div>
            </div>
            <div class="tech-item text-center">
              <div class="text-3xl mb-2">⚡</div>
              <div class="font-semibold text-gray-900 dark:text-white">Vite</div>
              <div class="text-sm text-gray-600 dark:text-gray-400">构建工具</div>
            </div>
            <div class="tech-item text-center">
              <div class="text-3xl mb-2">🍍</div>
              <div class="font-semibold text-gray-900 dark:text-white">Pinia</div>
              <div class="text-sm text-gray-600 dark:text-gray-400">状态管理</div>
            </div>
            <div class="tech-item text-center">
              <div class="text-3xl mb-2">🎨</div>
              <div class="font-semibold text-gray-900 dark:text-white">Tailwind CSS</div>
              <div class="text-sm text-gray-600 dark:text-gray-400">样式框架</div>
            </div>
            <div class="tech-item text-center">
              <div class="text-3xl mb-2">🧪</div>
              <div class="font-semibold text-gray-900 dark:text-white">Vitest</div>
              <div class="text-sm text-gray-600 dark:text-gray-400">单元测试</div>
            </div>
            <div class="tech-item text-center">
              <div class="text-3xl mb-2">🎭</div>
              <div class="font-semibold text-gray-900 dark:text-white">Playwright</div>
              <div class="text-sm text-gray-600 dark:text-gray-400">E2E测试</div>
            </div>
            <div class="tech-item text-center">
              <div class="text-3xl mb-2">✅</div>
              <div class="font-semibold text-gray-900 dark:text-white">ESLint</div>
              <div class="text-sm text-gray-600 dark:text-gray-400">代码规范</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 统计数据 -->
    <section class="stats mb-16">
      <div class="max-w-4xl mx-auto">
        <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-2xl p-8 text-white">
          <h2 class="text-3xl font-bold text-center mb-8">
            📊 平台数据
          </h2>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div class="stat-item text-center">
              <div class="text-3xl font-bold mb-2">{{ stats.totalTools }}+</div>
              <div class="text-white/80">实用工具</div>
            </div>
            <div class="stat-item text-center">
              <div class="text-3xl font-bold mb-2">{{ stats.totalUsage.toLocaleString() }}+</div>
              <div class="text-white/80">使用次数</div>
            </div>
            <div class="stat-item text-center">
              <div class="text-3xl font-bold mb-2">{{ stats.categories }}+</div>
              <div class="text-white/80">工具分类</div>
            </div>
            <div class="stat-item text-center">
              <div class="text-3xl font-bold mb-2">{{ stats.dailyUsers }}+</div>
              <div class="text-white/80">日活用户</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 联系方式 -->
    <section class="contact">
      <div class="max-w-4xl mx-auto text-center">
        <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-6">
          📧 联系我们
        </h2>
        <p class="text-lg text-gray-600 dark:text-gray-300 mb-8">
          如果您有任何建议、问题或合作意向，欢迎随时联系我们
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a 
            href="mailto:<EMAIL>" 
            class="inline-flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            发送邮件
          </a>
          <a 
            href="https://github.com/toolhub" 
            target="_blank" 
            rel="noopener noreferrer"
            class="inline-flex items-center justify-center gap-2 px-6 py-3 bg-gray-800 dark:bg-gray-700 text-white rounded-lg hover:bg-gray-900 dark:hover:bg-gray-600 transition-colors font-medium"
          >
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
            </svg>
            GitHub
          </a>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useToolsStore } from '@/stores/tools'

const toolsStore = useToolsStore()

// 统计数据
const stats = computed(() => {
  const tools = toolsStore.tools
  const totalUsage = tools.reduce((sum, tool) => sum + tool.usageCount, 0)
  
  return {
    totalTools: tools.length,
    totalUsage,
    categories: toolsStore.categories.length,
    dailyUsers: Math.floor(totalUsage / 30) // 模拟日活数据
  }
})

// 生命周期
onMounted(() => {
  // 更新页面标题
  document.title = '关于我们 - ToolHub'
})
</script>

<style scoped>
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  border-radius: 1rem;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.dark .hero-section {
  background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
}

.feature-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.dark .feature-card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

.tech-item {
  transition: transform 0.2s ease;
}

.tech-item:hover {
  transform: scale(1.05);
}

.stat-item {
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>