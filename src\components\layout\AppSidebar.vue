<template>
  <aside class="w-64 flex-shrink-0 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 p-4">
    <nav>
      <ul>
        <li v-for="category in categories" :key="category.id">
          <router-link 
            :to="`/tools/${category.id}`"
            class="flex items-center px-4 py-2 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
            active-class="bg-gray-100 dark:bg-gray-700 font-semibold"
          >
            {{ category.name }}
          </router-link>
        </li>
      </ul>
    </nav>
  </aside>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useToolsStore } from '@/stores/tools'

const toolsStore = useToolsStore()
const categories = computed(() => toolsStore.categories)
</script>