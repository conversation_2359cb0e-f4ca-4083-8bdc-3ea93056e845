<template>
  <div id="app" class="h-screen w-screen bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200 font-sans">
    <div class="flex flex-col h-full">
        <!-- Header -->
        <AppHeader />

        <!-- Content Area -->
        <main class="flex-1 overflow-y-auto p-6 lg:p-8 w-full">
          <RouterView v-slot="{ Component, route }">
            <Transition name="fade" mode="out-in">
              <component :is="Component" :key="route.path" />
            </Transition>
          </RouterView>
        </main>
    </div>

    <!-- Global Components -->
    <NotificationContainer />
    <BackToTop />
  </div>
</template>

<script setup lang="ts">
import { onMounted, watch } from 'vue'
import { useThemeStore } from '@/stores/theme'
import AppHeader from '@/components/layout/AppHeader.vue'

import NotificationContainer from '@/components/common/NotificationContainer.vue'
import BackToTop from '@/components/common/BackToTop.vue'

const themeStore = useThemeStore()

onMounted(() => {
  themeStore.initTheme()
})

// Watch for theme changes and update the DOM
watch(
  () => themeStore.isDark,
  (isDark) => {
    const html = document.documentElement
    if (isDark) {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }
  },
  { immediate: true }
)
</script>

<style>
/* Basic styles, can be expanded */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Custom scrollbar for a more modern look */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-track {
  background: transparent;
}
::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.4);
  border-radius: 3px;
}
.dark ::-webkit-scrollbar-thumb {
  background-color: rgba(107, 114, 128, 0.4);
}
::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.6);
}
.dark ::-webkit-scrollbar-thumb:hover {
  background-color: rgba(107, 114, 128, 0.6);
}
</style>
