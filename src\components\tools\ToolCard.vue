<template>
  <div 
    class="tool-card group relative flex flex-col h-full bg-white dark:bg-gray-800 rounded-lg shadow-sm transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
    :class="{
      'border border-transparent hover:border-blue-500': !isCompact,
      'p-3': !isCompact,
      'p-2 flex-row items-center gap-3': isCompact,
      'ring-1 ring-blue-500': isFavorite,
      'opacity-70 grayscale': !tool.isActive
    }"
  >
    <!-- Icon -->
    <div 
      class="flex-shrink-0 flex items-center justify-center rounded-md"
      :class="[isCompact ? 'w-10 h-10' : 'w-14 h-14 mx-auto mb-3', getIconBgColor(tool.category)]"
    >
      <img v-if="tool.icon && tool.icon.startsWith('http')" :src="tool.icon" :alt="tool.name" class="w-7 h-7" @error="onIconError">
      <span v-else-if="tool.icon" class="text-3xl">{{ tool.icon }}</span>
      <svg v-else class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 16v-2m8-6h2M4 12H2m15.364 6.364l1.414 1.414M4.222 4.222l1.414 1.414m12.728 0l-1.414 1.414M5.636 18.364l-1.414 1.414M12 18a6 6 0 100-12 6 6 0 000 12z"></path></svg>
    </div>

    <!-- Content -->
    <div class="flex flex-col flex-grow" :class="{'text-left': isCompact}">
      <h3 
        class="font-semibold text-gray-800 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400"
        :class="isCompact ? 'text-sm' : 'text-base text-center'"
      >
        {{ tool.name }}
      </h3>
      <p v-if="!isCompact" class="text-xs text-gray-500 dark:text-gray-400 mt-1 text-center flex-grow line-clamp-2">
        {{ tool.description }}
      </p>
      
      <!-- Compact view shows tags -->
      <div v-if="isCompact" class="flex flex-wrap gap-1 mt-1">
         <span v-for="tag in tool.tags?.slice(0, 2)" :key="tag" class="px-1.5 py-0.5 rounded-full text-xs font-medium bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300">
          {{ tag }}
        </span>
      </div>
    </div>

    <!-- Actions (non-compact) -->
    <div v-if="!isCompact" class="mt-3 pt-3 border-t border-gray-100 dark:border-gray-700/50 flex items-center justify-between">
      <div class="flex items-center gap-2 text-xs text-gray-500">
        <span class="flex items-center gap-1" title="收藏数">
          <svg class="w-3 h-3 text-red-400" fill="currentColor" viewBox="0 0 20 20"><path d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"/></svg>
          {{ favoritesCount }}
        </span>
        <span class="flex items-center gap-1" title="使用次数">
           <svg class="w-3 h-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a2 2 0 01-2-2V8a2 2 0 012-2h6z"></path></svg>
          {{ formatUsageCount(tool.usageCount) }}
        </span>
      </div>
      <button @click.stop="toggleFavorite" class="p-1.5 rounded-full hover:bg-red-100 dark:hover:bg-red-900/50" :title="isFavorite ? '取消收藏' : '收藏'">
        <svg class="w-4 h-4 transition-colors" :class="isFavorite ? 'text-red-500' : 'text-gray-400 hover:text-red-500'" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"/></svg>
      </button>
    </div>

    <!-- Status Badge -->
    <div class="absolute top-1.5 right-1.5 flex gap-1">
       <span v-if="tool.isNew" class="inline-block px-1.5 py-0.5 text-xs font-semibold text-green-800 bg-green-200 rounded-full">新</span>
       <span v-if="tool.isHot" class="inline-block px-1.5 py-0.5 text-xs font-semibold text-red-800 bg-red-200 rounded-full">热</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import type { Tool } from '@/types'
import { useFavoritesStore } from '@/stores/favorites'
import { useNotificationStore } from '@/stores/notification'

const getIconBgColor = (category: string) => {
  const colors: Record<string, string> = {
    development: 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400',
    encoding: 'bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400',
    conversion: 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400',
    text: 'bg-yellow-100 dark:bg-yellow-900 text-yellow-600 dark:text-yellow-400',
    image: 'bg-pink-100 dark:bg-pink-900 text-pink-600 dark:text-pink-400',
    default: 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400',
  }
  return colors[category] || colors.default
}

const props = defineProps<{
  tool: Tool
  isCompact?: boolean
}>()

const favoritesStore = useFavoritesStore()
const notificationStore = useNotificationStore()

const isFavorite = computed(() => favoritesStore.isFavorite(props.tool.id))
// Mock favorites count for display
const favoritesCount = ref(Math.floor(Math.random() * 500) + 10)

const toggleFavorite = () => {
  if (isFavorite.value) {
    favoritesStore.removeFavorite(props.tool.id)
    notificationStore.success('已取消收藏', `${props.tool.name} 已从收藏夹中移除`)
    favoritesCount.value--
  } else {
    favoritesStore.addFavorite(props.tool)
    notificationStore.success('添加收藏成功', `${props.tool.name} 已添加到收藏夹`)
    favoritesCount.value++
  }
}

const onIconError = (e: Event) => {
  (e.target as HTMLImageElement).style.display = 'none';
  // You could show a fallback icon here if you want
}

const formatUsageCount = (count: number = 0): string => {
  if (count < 1000) return count.toString()
  if (count < 10000) return `${(count / 1000).toFixed(1)}k`
  return `${Math.floor(count / 1000)}k+`
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
