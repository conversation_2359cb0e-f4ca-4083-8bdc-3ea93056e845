<template>
  <div
    class="tool-card group relative flex flex-col h-full ghibli-card transition-all duration-300 hover:shadow-xl hover:-translate-y-2"
    :class="{
      'p-4': !isCompact,
      'p-3 flex-row items-center gap-3': isCompact,
      'ring-2 ring-blue-400/50': isFavorite,
      'opacity-70 grayscale': !tool.isActive
    }"
  >
    <!-- Icon -->
    <div
      class="flex-shrink-0 flex items-center justify-center rounded-2xl ghibli-icon transition-all duration-300 group-hover:scale-110"
      :class="[isCompact ? 'w-10 h-10' : 'w-16 h-16 mx-auto mb-4', getIconBgColor(tool.category)]"
    >
      <img v-if="tool.icon && tool.icon.startsWith('http')" :src="tool.icon" :alt="tool.name" class="w-7 h-7" @error="onIconError">
      <span v-else-if="tool.icon" class="text-3xl">{{ tool.icon }}</span>
      <svg v-else class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 16v-2m8-6h2M4 12H2m15.364 6.364l1.414 1.414M4.222 4.222l1.414 1.414m12.728 0l-1.414 1.414M5.636 18.364l-1.414 1.414M12 18a6 6 0 100-12 6 6 0 000 12z"></path></svg>
    </div>

    <!-- Content -->
    <div class="flex flex-col flex-grow" :class="{'text-left': isCompact}">
      <h3
        class="font-semibold text-slate-700 dark:text-slate-200 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300"
        :class="isCompact ? 'text-sm' : 'text-lg text-center'"
      >
        {{ tool.name }}
      </h3>
      <p v-if="!isCompact" class="text-sm text-slate-500 dark:text-slate-400 mt-2 text-center flex-grow line-clamp-2 leading-relaxed">
        {{ tool.description }}
      </p>
      
      <!-- Compact view shows tags -->
      <div v-if="isCompact" class="flex flex-wrap gap-1 mt-1">
         <span v-for="tag in tool.tags?.slice(0, 2)" :key="tag" class="px-1.5 py-0.5 rounded-full text-xs font-medium bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300">
          {{ tag }}
        </span>
      </div>
    </div>

    <!-- Actions (non-compact) -->
    <div v-if="!isCompact" class="mt-4 pt-4 border-t border-slate-200/60 dark:border-slate-600/40 flex items-center justify-between">
      <div class="flex items-center gap-3 text-sm text-slate-500 dark:text-slate-400">
        <span class="flex items-center gap-1.5" title="收藏数">
          <svg class="w-4 h-4 text-red-400" fill="currentColor" viewBox="0 0 20 20"><path d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"/></svg>
          {{ favoritesCount }}
        </span>
        <span class="flex items-center gap-1.5" title="使用次数">
           <svg class="w-4 h-4 text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a2 2 0 01-2-2V8a2 2 0 012-2h6z"></path></svg>
          {{ formatUsageCount(tool.usageCount) }}
        </span>
      </div>
      <button @click.stop="toggleFavorite" class="p-2 rounded-xl hover:bg-red-50 dark:hover:bg-red-900/30 transition-all duration-300 ghibli-button" :title="isFavorite ? '取消收藏' : '收藏'">
        <svg class="w-5 h-5 transition-colors duration-300" :class="isFavorite ? 'text-red-500' : 'text-slate-400 hover:text-red-500'" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"/></svg>
      </button>
    </div>

    <!-- Status Badge -->
    <div class="absolute top-1.5 right-1.5 flex gap-1">
       <span v-if="tool.isNew" class="inline-block px-1.5 py-0.5 text-xs font-semibold text-green-800 bg-green-200 rounded-full">新</span>
       <span v-if="tool.isHot" class="inline-block px-1.5 py-0.5 text-xs font-semibold text-red-800 bg-red-200 rounded-full">热</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import type { Tool } from '@/types'
import { useFavoritesStore } from '@/stores/favorites'
import { useNotificationStore } from '@/stores/notification'

const getIconBgColor = (category: string) => {
  const colors: Record<string, string> = {
    development: 'text-blue-500 dark:text-blue-400',
    encoding: 'text-purple-500 dark:text-purple-400',
    conversion: 'text-green-500 dark:text-green-400',
    text: 'text-amber-500 dark:text-amber-400',
    image: 'text-pink-500 dark:text-pink-400',
    generator: 'text-indigo-500 dark:text-indigo-400',
    default: 'text-slate-500 dark:text-slate-400',
  }
  return colors[category] || colors.default
}

const props = defineProps<{
  tool: Tool
  isCompact?: boolean
}>()

const favoritesStore = useFavoritesStore()
const notificationStore = useNotificationStore()

const isFavorite = computed(() => favoritesStore.isFavorite(props.tool.id))
// Mock favorites count for display
const favoritesCount = ref(Math.floor(Math.random() * 500) + 10)

const toggleFavorite = () => {
  if (isFavorite.value) {
    favoritesStore.removeFavorite(props.tool.id)
    notificationStore.success('已取消收藏', `${props.tool.name} 已从收藏夹中移除`)
    favoritesCount.value--
  } else {
    favoritesStore.addFavorite(props.tool)
    notificationStore.success('添加收藏成功', `${props.tool.name} 已添加到收藏夹`)
    favoritesCount.value++
  }
}

const onIconError = (e: Event) => {
  (e.target as HTMLImageElement).style.display = 'none';
  // You could show a fallback icon here if you want
}

const formatUsageCount = (count: number = 0): string => {
  if (count < 1000) return count.toString()
  if (count < 10000) return `${(count / 1000).toFixed(1)}k`
  return `${Math.floor(count / 1000)}k+`
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Ghibli-inspired card styling */
.ghibli-card {
  /* Soft pastoral gradient background */
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 30%, #e2e8f0 100%);
  border-radius: 20px; /* Organic, rounded shape */
  border: 1px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 6px 24px rgba(148, 163, 184, 0.15);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.ghibli-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(168, 216, 234, 0.1) 0%, rgba(213, 232, 212, 0.1) 60%, rgba(252, 223, 166, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.ghibli-card:hover::before {
  opacity: 1;
}

.ghibli-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(148, 163, 184, 0.25);
  border-color: rgba(168, 216, 234, 0.6);
}

/* Dark mode styling */
.dark .ghibli-card {
  background: linear-gradient(135deg, #1e293b 0%, #334155 30%, #475569 100%);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.2);
}

.dark .ghibli-card::before {
  background: linear-gradient(135deg, rgba(44, 62, 80, 0.2) 0%, rgba(74, 85, 104, 0.2) 100%);
}

.dark .ghibli-card:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
  border-color: rgba(96, 165, 250, 0.4);
}

/* Ghibli-inspired icon styling */
.ghibli-icon {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
  border: 1px solid rgba(255, 255, 255, 0.6);
  box-shadow: 0 4px 12px rgba(148, 163, 184, 0.1);
  backdrop-filter: blur(5px);
}

.dark .ghibli-icon {
  background: linear-gradient(135deg, rgba(51, 65, 85, 0.9) 0%, rgba(30, 41, 59, 0.8) 100%);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Ghibli-inspired button styling */
.ghibli-button {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.6) 100%);
  border: 1px solid rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(5px);
}

.dark .ghibli-button {
  background: linear-gradient(135deg, rgba(51, 65, 85, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%);
  border-color: rgba(255, 255, 255, 0.1);
}
</style>
