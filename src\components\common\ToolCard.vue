<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 group">
    <!-- Tool Header -->
    <div class="p-6">
      <div class="flex items-start justify-between mb-4">
        <div class="flex items-center space-x-3">
          <!-- Tool Icon -->
          <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white text-xl font-bold group-hover:scale-105 transition-transform">
            {{ tool.icon || tool.name.charAt(0).toUpperCase() }}
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
              {{ tool.name }}
            </h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              {{ categoryName }}
            </p>
          </div>
        </div>
        
        <!-- Favorite Button -->
        <button
          @click.stop="toggleFavorite"
          class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          :title="isFavorite ? '取消收藏' : '添加收藏'"
        >
          <svg 
            class="w-5 h-5 transition-colors"
            :class="isFavorite ? 'text-red-500 fill-current' : 'text-gray-400 hover:text-red-500'"
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              stroke-linecap="round" 
              stroke-linejoin="round" 
              stroke-width="2" 
              d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" 
            />
          </svg>
        </button>
      </div>

      <!-- Tool Description -->
      <p class="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2">
        {{ tool.description }}
      </p>

      <!-- Tool Tags -->
      <div class="flex flex-wrap gap-2 mb-4">
        <span
          v-for="tag in tool.tags.slice(0, 3)"
          :key="tag"
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
        >
          {{ tag }}
        </span>
        <span
          v-if="tool.tags.length > 3"
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400"
        >
          +{{ tool.tags.length - 3 }}
        </span>
      </div>

      <!-- Tool Stats -->
      <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-1">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            <span>{{ formatUsageCount(tool.usageCount) }}</span>
          </div>
          <div class="flex items-center space-x-1">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{{ formatDate(tool.updatedAt) }}</span>
          </div>
        </div>
        
        <!-- Badges -->
        <div class="flex items-center space-x-2">
          <span
            v-if="tool.isPopular"
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"
          >
            🔥 热门
          </span>
          <span
            v-if="tool.isFree"
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
          >
            免费
          </span>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex space-x-2">
        <router-link
          :to="`/tools/${tool.id}`"
          class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-lg text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          使用工具
        </router-link>
        <button
          @click.stop="shareTool"
          class="p-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          title="分享工具"
        >
          <svg class="w-4 h-4 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useToolsStore } from '@/stores/tools'
import type { Tool } from '@/types/tool'

interface Props {
  tool: Tool
}

const props = defineProps<Props>()

const toolsStore = useToolsStore()

// 计算属性
const isFavorite = computed(() => {
  return toolsStore.favoriteTools.includes(props.tool.id)
})

const categoryName = computed(() => {
  const category = toolsStore.categories.find(cat => cat.id === props.tool.category)
  return category?.name || '其他'
})

// 方法
const toggleFavorite = () => {
  toolsStore.toggleFavorite(props.tool.id)
}

const shareTool = async () => {
  const url = `${window.location.origin}/tools/${props.tool.id}`
  const text = `${props.tool.name} - ${props.tool.description}`

  if (navigator.share) {
    try {
      await navigator.share({
        title: props.tool.name,
        text: text,
        url: url
      })
    } catch (error) {
      console.log('分享取消或失败:', error)
    }
  } else {
    // 降级到复制链接
    try {
      await navigator.clipboard.writeText(url)
      // 这里可以添加一个 toast 提示
      console.log('链接已复制到剪贴板')
    } catch (error) {
      console.error('复制失败:', error)
    }
  }
}

const formatUsageCount = (count: number): string => {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K`
  }
  return count.toString()
}

const formatDate = (date: string): string => {
  const now = new Date()
  const targetDate = new Date(date)
  const diffTime = Math.abs(now.getTime() - targetDate.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === 1) {
    return '1天前'
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else if (diffDays < 30) {
    const weeks = Math.floor(diffDays / 7)
    return `${weeks}周前`
  } else if (diffDays < 365) {
    const months = Math.floor(diffDays / 30)
    return `${months}月前`
  } else {
    const years = Math.floor(diffDays / 365)
    return `${years}年前`
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>