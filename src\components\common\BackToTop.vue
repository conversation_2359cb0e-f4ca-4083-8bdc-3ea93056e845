<template>
  <Transition
    name="back-to-top"
    appear
  >
    <button
      v-show="isVisible"
      @click="scrollToTop"
      :class="[
        'fixed bottom-6 right-6 z-40 p-3 rounded-full shadow-lg transition-all duration-300',
        'bg-primary-600 hover:bg-primary-700 text-white',
        'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
        'transform hover:scale-110 active:scale-95',
        isScrolling ? 'opacity-50' : 'opacity-100'
      ]"
      :title="'回到顶部'"
      :aria-label="'回到顶部'"
    >
      <ChevronUpIcon class="h-6 w-6" />
      
      <!-- 进度环 -->
      <svg
        v-if="showProgress"
        class="absolute inset-0 w-full h-full -rotate-90"
        viewBox="0 0 36 36"
      >
        <path
          class="stroke-current text-primary-200 opacity-30"
          stroke-width="2"
          fill="none"
          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
        />
        <path
          class="stroke-current text-white transition-all duration-300 ease-out"
          stroke-width="2"
          fill="none"
          stroke-linecap="round"
          :stroke-dasharray="`${scrollProgress}, 100`"
          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
        />
      </svg>
    </button>
  </Transition>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ChevronUpIcon } from '@heroicons/vue/24/outline'

interface Props {
  threshold?: number // 显示按钮的滚动阈值
  showProgress?: boolean // 是否显示滚动进度
  smooth?: boolean // 是否平滑滚动
  duration?: number // 滚动动画持续时间
}

const props = withDefaults(defineProps<Props>(), {
  threshold: 300,
  showProgress: true,
  smooth: true,
  duration: 800,
})

const scrollY = ref(0)
const isScrolling = ref(false)
const documentHeight = ref(0)
const windowHeight = ref(0)

// 计算属性
const isVisible = computed(() => scrollY.value > props.threshold)

const scrollProgress = computed(() => {
  if (documentHeight.value <= windowHeight.value) return 0
  const maxScroll = documentHeight.value - windowHeight.value
  return Math.min(100, (scrollY.value / maxScroll) * 100)
})

// 滚动事件处理
function handleScroll() {
  scrollY.value = window.scrollY
  documentHeight.value = document.documentElement.scrollHeight
  windowHeight.value = window.innerHeight
}

// 滚动到顶部
function scrollToTop() {
  if (!props.smooth) {
    window.scrollTo(0, 0)
    return
  }

  isScrolling.value = true
  
  const startPosition = window.scrollY
  const startTime = performance.now()
  
  function animateScroll(currentTime: number) {
    const elapsed = currentTime - startTime
    const progress = Math.min(elapsed / props.duration, 1)
    
    // 使用 easeOutCubic 缓动函数
    const easeProgress = 1 - Math.pow(1 - progress, 3)
    const currentPosition = startPosition * (1 - easeProgress)
    
    window.scrollTo(0, currentPosition)
    
    if (progress < 1) {
      requestAnimationFrame(animateScroll)
    } else {
      isScrolling.value = false
    }
  }
  
  requestAnimationFrame(animateScroll)
}

// 节流函数
function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  return function (this: any, ...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

// 节流后的滚动处理函数
const throttledHandleScroll = throttle(handleScroll, 16) // ~60fps

// 键盘事件处理
function handleKeydown(event: KeyboardEvent) {
  // Home 键回到顶部
  if (event.key === 'Home' && event.ctrlKey) {
    event.preventDefault()
    scrollToTop()
  }
}

// 生命周期
onMounted(() => {
  // 初始化
  handleScroll()
  
  // 添加事件监听
  window.addEventListener('scroll', throttledHandleScroll, { passive: true })
  window.addEventListener('resize', handleScroll, { passive: true })
  window.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener('scroll', throttledHandleScroll)
  window.removeEventListener('resize', handleScroll)
  window.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.back-to-top-enter-active {
  transition: all 0.3s ease-out;
}

.back-to-top-leave-active {
  transition: all 0.3s ease-in;
}

.back-to-top-enter-from {
  opacity: 0;
  transform: translateY(20px) scale(0.8);
}

.back-to-top-leave-to {
  opacity: 0;
  transform: translateY(20px) scale(0.8);
}

/* 移动端适配 */
@media (max-width: 640px) {
  button {
    bottom: 1rem;
    right: 1rem;
    padding: 0.75rem;
  }
  
  .h-6 {
    height: 1.25rem;
    width: 1.25rem;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  button {
    border: 2px solid currentColor;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .back-to-top-enter-active,
  .back-to-top-leave-active {
    transition: opacity 0.1s ease;
  }
  
  .back-to-top-enter-from,
  .back-to-top-leave-to {
    transform: none;
  }
  
  button {
    transition: opacity 0.1s ease;
  }
}
</style>