import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '@/views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      meta: {
        title: 'ToolHub - 在线工具集平台'
      }
    },
    {
      path: '/tools/:category?',
      name: 'tools',
      component: () => import('@/views/ToolsView.vue'),
      meta: {
        title: '工具列表 - ToolHub'
      }
    },
    {
      path: '/tool/:id',
      name: 'tool-detail',
      component: () => import('@/views/ToolDetailView.vue'),
      meta: {
        title: '工具详情 - ToolHub'
      }
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('@/views/AboutView.vue'),
      meta: {
        title: '关于我们 - ToolHub'
      }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('@/views/NotFoundView.vue'),
      meta: {
        title: '页面未找到 - ToolHub'
      }
    }
  ]
})

// 路由守卫 - 设置页面标题
router.beforeEach((to) => {
  if (to.meta.title) {
    document.title = to.meta.title as string
  }
})

export default router