<template>
  <div 
    class="category-card group relative cursor-pointer rounded-xl bg-white p-5 shadow-sm transition-all duration-300 hover:shadow-md hover:-translate-y-0.5 dark:bg-gray-800"
    :class="{
      'ring-2 ring-blue-500 dark:ring-blue-400': isSelected,
      'opacity-60 grayscale': disabled
    }"
    @click="onClick"
    tabindex="0"
    @keydown.enter.space="onClick"
  >
    <div class="flex items-start gap-4">
      <!-- Icon -->
      <div 
        class="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-lg transition-colors duration-300"
        :class="[isSelected ? 'bg-blue-100 dark:bg-blue-900/50' : 'bg-gray-100 dark:bg-gray-700 group-hover:bg-blue-50 dark:group-hover:bg-blue-900/30']"
      >
        <span class="text-2xl">{{ category.icon }}</span>
      </div>
      
      <!-- Content -->
      <div class="flex-1">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-800 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400">
            {{ category.name }}
          </h3>
          <span class="ml-2 text-sm font-medium text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 rounded-full px-2.5 py-0.5">
            {{ toolCount }}
          </span>
        </div>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
          {{ category.description }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { ToolCategory, Tool } from '@/types'

const props = defineProps<{
  category: ToolCategory
  tools?: Tool[]
  isSelected?: boolean
  disabled?: boolean
}>()

const emit = defineEmits<{
  (e: 'select', category: ToolCategory): void
}>()

const toolCount = computed(() => props.tools?.length || 0)

const onClick = () => {
  if (!props.disabled) {
    emit('select', props.category)
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.category-card:focus-visible {
  outline: none;
  @apply ring-2 ring-offset-2 ring-blue-500 dark:ring-offset-gray-900;
}
</style>
