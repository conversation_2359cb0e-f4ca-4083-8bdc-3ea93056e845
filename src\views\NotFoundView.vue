<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
      <!-- 404 Illustration -->
      <div class="mb-8">
        <div class="mx-auto w-32 h-32 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-6">
          <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.467-.881-6.08-2.33M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
          </svg>
        </div>
        <h1 class="text-6xl font-bold text-gray-900 dark:text-white mb-4">404</h1>
        <h2 class="text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
          页面未找到
        </h2>
        <p class="text-gray-500 dark:text-gray-400 mb-8">
          抱歉，您访问的页面不存在或已被移动。
        </p>
      </div>

      <!-- Action Buttons -->
      <div class="space-y-4">
        <router-link
          to="/"
          class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
        >
          返回首页
        </router-link>
        
        <router-link
          to="/tools"
          class="w-full flex justify-center py-3 px-4 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
        >
          浏览工具
        </router-link>
        
        <button
          @click="goBack"
          class="w-full flex justify-center py-3 px-4 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
        >
          返回上一页
        </button>
      </div>

      <!-- Search Box -->
      <div class="mt-8">
        <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
          或者搜索您需要的工具：
        </p>
        <SearchBox 
          placeholder="搜索工具..."
          @search="handleSearch"
        />
      </div>

      <!-- Popular Tools -->
      <div class="mt-8">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
          热门工具
        </h3>
        <div class="grid grid-cols-1 gap-3">
          <router-link
            v-for="tool in popularTools.slice(0, 3)"
            :key="tool.id"
            :to="`/tools/${tool.id}`"
            class="flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white text-sm font-bold">
              {{ tool.icon || tool.name.charAt(0) }}
            </div>
            <div class="flex-1 text-left">
              <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                {{ tool.name }}
              </h4>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                {{ tool.description }}
              </p>
            </div>
            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </router-link>
        </div>
      </div>

      <!-- Help Text -->
      <div class="mt-8 text-center">
        <p class="text-xs text-gray-400 dark:text-gray-500">
          如果您认为这是一个错误，请
          <a href="mailto:<EMAIL>" class="text-blue-600 dark:text-blue-400 hover:underline">
            联系我们
          </a>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToolsStore } from '@/stores/tools'
import SearchBox from '@/components/common/SearchBox.vue'

const router = useRouter()
const toolsStore = useToolsStore()

// 计算属性
const popularTools = computed(() => {
  return toolsStore.popularTools
})

// 方法
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}

const handleSearch = (query: string) => {
  router.push({ path: '/tools', query: { search: query } })
}

// 生命周期
onMounted(() => {
  document.title = '页面未找到 - ToolHub'
})
</script>