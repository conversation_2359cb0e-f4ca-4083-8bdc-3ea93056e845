<template>
  <router-link
    :to="categoryLink"
    class="block bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 group"
  >
    <div class="p-6">
      <!-- Category Header -->
      <div class="flex items-center space-x-4 mb-4">
        <!-- Category Icon -->
        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white text-xl group-hover:scale-105 transition-transform">
          {{ category.icon || category.name.charAt(0) }}
        </div>
        <div class="flex-1">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
            {{ category.name }}
          </h3>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ toolCount }} 个工具
          </p>
        </div>
        <!-- Arrow Icon -->
        <div class="text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 group-hover:translate-x-1 transition-all">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </div>
      </div>

      <!-- Category Description -->
      <p class="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2">
        {{ category.description }}
      </p>

      <!-- Popular Tools Preview -->
      <div v-if="popularTools.length > 0" class="space-y-2">
        <h4 class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
          热门工具
        </h4>
        <div class="flex flex-wrap gap-2">
          <span
            v-for="tool in popularTools.slice(0, 3)"
            :key="tool.id"
            class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 border border-blue-200 dark:border-blue-800"
          >
            {{ tool.name }}
          </span>
          <span
            v-if="popularTools.length > 3"
            class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-50 text-gray-600 dark:bg-gray-700 dark:text-gray-400"
          >
            +{{ popularTools.length - 3 }}
          </span>
        </div>
      </div>

      <!-- Category Stats -->
      <div class="flex items-center justify-between mt-4 pt-4 border-t border-gray-100 dark:border-gray-700">
        <div class="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
          <div class="flex items-center space-x-1">
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            <span>{{ totalUsage }} 次使用</span>
          </div>
          <div class="flex items-center space-x-1">
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{{ lastUpdated }}</span>
          </div>
        </div>
        
        <!-- Category Badge -->
        <div class="flex items-center space-x-1">
          <span
            v-if="isPopularCategory"
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"
          >
            🔥 热门
          </span>
          <span
            v-if="hasNewTools"
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
          >
            ✨ 新增
          </span>
        </div>
      </div>
    </div>
  </router-link>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useToolsStore } from '@/stores/tools'
import type { ToolCategory } from '@/types/tool'

interface Props {
  category: ToolCategory
}

const props = defineProps<Props>()

const toolsStore = useToolsStore()

// 计算属性
const categoryTools = computed(() => {
  return toolsStore.tools.filter(tool => tool.category === props.category.id)
})

const toolCount = computed(() => {
  return categoryTools.value.length
})

const popularTools = computed(() => {
  return categoryTools.value
    .filter(tool => tool.isPopular || tool.usageCount > 1000)
    .sort((a, b) => b.usageCount - a.usageCount)
})

const totalUsage = computed(() => {
  const total = categoryTools.value.reduce((sum, tool) => sum + tool.usageCount, 0)
  return formatUsageCount(total)
})

const lastUpdated = computed(() => {
  const latestTool = categoryTools.value
    .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())[0]
  
  if (!latestTool) return '暂无更新'
  
  return formatDate(latestTool.updatedAt)
})

const isPopularCategory = computed(() => {
  return categoryTools.value.filter(tool => tool.isPopular).length >= 3
})

const hasNewTools = computed(() => {
  const oneWeekAgo = new Date()
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
  
  return categoryTools.value.some(tool => 
    new Date(tool.createdAt) > oneWeekAgo
  )
})

const categoryLink = computed(() => {
  return {
    path: '/tools',
    query: { category: props.category.id }
  }
})

// 方法
const formatUsageCount = (count: number): string => {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K`
  }
  return count.toString()
}

const formatDate = (date: string): string => {
  const now = new Date()
  const targetDate = new Date(date)
  const diffTime = Math.abs(now.getTime() - targetDate.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === 1) {
    return '1天前'
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else if (diffDays < 30) {
    const weeks = Math.floor(diffDays / 7)
    return `${weeks}周前`
  } else if (diffDays < 365) {
    const months = Math.floor(diffDays / 30)
    return `${months}月前`
  } else {
    const years = Math.floor(diffDays / 365)
    return `${years}年前`
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>