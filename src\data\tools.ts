import type { Tool, ToolCategory } from '@/types/tool'

// 工具分类数据
export const toolCategories: ToolCategory[] = [
  {
    id: 'dev-tools',
    name: '开发工具',
    description: '程序员必备的开发辅助工具',
    icon: '🛠️',
    color: 'blue',
    toolCount: 25
  },
  {
    id: 'encoding',
    name: '编码/加密',
    description: '各种编码转换和加密解密工具',
    icon: '🔐',
    color: 'green',
    toolCount: 18
  },
  {
    id: 'converter',
    name: '转换工具',
    description: '格式转换、单位换算等工具',
    icon: '🔄',
    color: 'purple',
    toolCount: 22
  },
  {
    id: 'text-tools',
    name: '文本工具',
    description: '文本处理、格式化等工具',
    icon: '📝',
    color: 'orange',
    toolCount: 20
  },
  {
    id: 'image-tools',
    name: '图片工具',
    description: '图片处理、压缩、格式转换',
    icon: '🖼️',
    color: 'pink',
    toolCount: 15
  },
  {
    id: 'generators',
    name: '生成器',
    description: '各种内容生成工具',
    icon: '⚡',
    color: 'yellow',
    toolCount: 12
  },
  {
    id: 'validators',
    name: '验证工具',
    description: '数据验证和检测工具',
    icon: '✅',
    color: 'emerald',
    toolCount: 10
  },
  {
    id: 'calculators',
    name: '计算器',
    description: '各种专业计算工具',
    icon: '🧮',
    color: 'indigo',
    toolCount: 16
  },
  {
    id: 'network-tools',
    name: '网络工具',
    description: '网络诊断和分析工具',
    icon: '🌐',
    color: 'cyan',
    toolCount: 8
  },
  {
    id: 'time-tools',
    name: '时间工具',
    description: '时间转换和计算工具',
    icon: '⏰',
    color: 'red',
    toolCount: 6
  },
  {
    id: 'other-tools',
    name: '其他工具',
    description: '其他实用工具',
    icon: '🔧',
    color: 'gray',
    toolCount: 8
  }
]

// 模拟工具数据
export const mockTools: Tool[] = [
  // 开发工具
  {
    id: 'json-formatter',
    name: 'JSON格式化',
    description: '格式化、压缩和验证JSON数据',
    category: 'dev-tools',
    tags: ['json', '格式化', '验证'],
    icon: '📋',
    isPopular: true,
    isFree: true,
    usageCount: 1250,
    type: 'vue',
    component: 'JsonFormatter',
    createdAt: '2024-01-15',
    updatedAt: '2024-01-20'
  },
  {
    id: 'regex-tester',
    name: '正则表达式测试',
    description: '测试和调试正则表达式',
    category: 'dev-tools',
    tags: ['正则', '测试', '调试'],
    icon: '🔍',
    isPopular: true,
    isFree: true,
    usageCount: 980,
    type: 'vue',
    component: 'RegexTester',
    createdAt: '2024-01-10',
    updatedAt: '2024-01-18'
  },
  {
    id: 'color-picker',
    name: '颜色选择器',
    description: '选择颜色并获取各种格式的颜色值',
    category: 'dev-tools',
    tags: ['颜色', '设计', 'css'],
    icon: '🎨',
    isPopular: true,
    isFree: true,
    usageCount: 850,
    type: 'vue',
    component: 'ColorPicker',
    createdAt: '2024-01-12',
    updatedAt: '2024-01-19'
  },
  {
    id: 'sql-formatter',
    name: 'SQL格式化',
    description: '格式化和美化SQL语句',
    category: 'dev-tools',
    tags: ['sql', '格式化', '数据库'],
    icon: '🗄️',
    isFree: true,
    usageCount: 420,
    type: 'vue',
    component: 'SqlFormatter',
    createdAt: '2024-01-08',
    updatedAt: '2024-01-16'
  },
  
  // 编码/加密工具
  {
    id: 'base64-encoder',
    name: 'Base64编码',
    description: 'Base64编码和解码工具',
    category: 'encoding',
    tags: ['base64', '编码', '解码'],
    icon: '🔤',
    isPopular: true,
    isFree: true,
    usageCount: 1100,
    type: 'vue',
    component: 'Base64Encoder',
    createdAt: '2024-01-05',
    updatedAt: '2024-01-17'
  },
  {
    id: 'url-encoder',
    name: 'URL编码',
    description: 'URL编码和解码工具',
    category: 'encoding',
    tags: ['url', '编码', '解码'],
    icon: '🔗',
    isPopular: true,
    isFree: true,
    usageCount: 890,
    type: 'vue',
    component: 'UrlEncoder',
    createdAt: '2024-01-06',
    updatedAt: '2024-01-15'
  },
  {
    id: 'md5-hash',
    name: 'MD5哈希',
    description: '生成文本的MD5哈希值',
    category: 'encoding',
    tags: ['md5', '哈希', '加密'],
    icon: '🔐',
    isFree: true,
    usageCount: 650,
    type: 'vue',
    component: 'Md5Hash',
    createdAt: '2024-01-07',
    updatedAt: '2024-01-14'
  },
  
  // 转换工具
  {
    id: 'timestamp-converter',
    name: '时间戳转换',
    description: '时间戳与日期时间相互转换',
    category: 'converter',
    tags: ['时间戳', '日期', '转换'],
    icon: '⏱️',
    isPopular: true,
    isFree: true,
    usageCount: 1350,
    type: 'vue',
    component: 'TimestampConverter',
    createdAt: '2024-01-03',
    updatedAt: '2024-01-21'
  },
  {
    id: 'unit-converter',
    name: '单位转换',
    description: '长度、重量、温度等单位转换',
    category: 'converter',
    tags: ['单位', '转换', '计算'],
    icon: '📏',
    isFree: true,
    usageCount: 520,
    type: 'vue',
    component: 'UnitConverter',
    createdAt: '2024-01-09',
    updatedAt: '2024-01-13'
  },
  
  // 文本工具
  {
    id: 'text-diff',
    name: '文本对比',
    description: '比较两段文本的差异',
    category: 'text-tools',
    tags: ['文本', '对比', '差异'],
    icon: '📊',
    isPopular: true,
    isFree: true,
    usageCount: 720,
    type: 'vue',
    component: 'TextDiff',
    createdAt: '2024-01-11',
    updatedAt: '2024-01-22'
  },
  {
    id: 'word-counter',
    name: '字数统计',
    description: '统计文本的字数、词数、行数等',
    category: 'text-tools',
    tags: ['字数', '统计', '文本'],
    icon: '📈',
    isFree: true,
    usageCount: 380,
    type: 'vue',
    component: 'WordCounter',
    createdAt: '2024-01-04',
    updatedAt: '2024-01-12'
  },
  
  // 图片工具
  {
    id: 'image-compressor',
    name: '图片压缩',
    description: '在线压缩图片，减小文件大小',
    category: 'image-tools',
    tags: ['图片', '压缩', '优化'],
    icon: '🗜️',
    isPopular: true,
    isFree: true,
    usageCount: 950,
    type: 'vue',
    component: 'ImageCompressor',
    config: {
      fileUpload: true,
      acceptedFileTypes: ['image/*'],
      maxFileSize: 10
    },
    createdAt: '2024-01-02',
    updatedAt: '2024-01-20'
  },
  
  // 生成器
  {
    id: 'qr-generator',
    name: '二维码生成',
    description: '生成各种内容的二维码',
    category: 'generators',
    tags: ['二维码', '生成', 'qr'],
    icon: '📱',
    isPopular: true,
    isFree: true,
    usageCount: 1180,
    type: 'vue',
    component: 'QrGenerator',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-23'
  },
  {
    id: 'password-generator',
    name: '密码生成器',
    description: '生成安全的随机密码',
    category: 'generators',
    tags: ['密码', '生成', '安全'],
    icon: '🔑',
    isPopular: true,
    isFree: true,
    usageCount: 1050,
    type: 'vue',
    component: 'PasswordGenerator',
    createdAt: '2024-01-14',
    updatedAt: '2024-01-21'
  }
]