<template>
  <aside class="right-sidebar w-56 flex-shrink-0">
    <!-- 最近更新 -->
    <section class="recent-tools mb-8">
      <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">🆕 最近更新</h3>
      <div class="space-y-3">
        <ToolCard 
          v-for="tool in recentTools" 
          :key="tool.id" 
          :tool="tool"
          :is-compact="true"
        />
      </div>
    </section>

    <!-- 特色工具 -->
    <section class="featured-tools">
      <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">⭐ 特色工具</h3>
      <div class="space-y-3">
        <ToolCard 
          v-for="tool in featuredTools" 
          :key="tool.id" 
          :tool="tool"
          :is-compact="true"
        />
      </div>
    </section>
  </aside>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useToolsStore } from '@/stores/tools'
import ToolCard from '@/components/tools/ToolCard.vue'

const toolsStore = useToolsStore()

const recentTools = computed(() => toolsStore.recentTools.slice(0, 5))
const featuredTools = computed(() => toolsStore.popularTools.slice(0, 5))
</script>

<style scoped>
.right-sidebar {
  /* 添加一些样式以区分右侧边栏 */
  padding: 1rem;
}
</style>
